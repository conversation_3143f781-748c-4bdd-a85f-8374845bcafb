/** xx.di.englxidn.com
 * 小红书自动点赞脚本 - 主程序
 * 作者: Claude
 * 日期: 2025-07-01
 */

// ===== 最高优先级：立即禁用所有权限申请 =====
console.log("🚫 ROOT模式启动 - 立即禁用所有权限申请");

// 第一阶段：立即禁用当前已存在的权限函数
(function () {
    try {
        if (typeof requestScreenCapture !== 'undefined') {
            requestScreenCapture = function () {
                console.log("🚫 requestScreenCapture被立即禁用");
                return true;
            };
        }
        if (typeof captureScreen !== 'undefined') {
            captureScreen = function () {
                console.log("🚫 captureScreen被立即禁用");
                return null;
            };
        }
        if (typeof images !== 'undefined') {
            if (images.captureScreen) images.captureScreen = function () { return null; };
            if (images.requestScreenCapture) images.requestScreenCapture = function () { return true; };
            if (images.requestScreenCaptureAsync) images.requestScreenCaptureAsync = function () { return Promise.resolve(true); };
        }
        if (typeof auto !== 'undefined') {
            if (auto.waitFor) auto.waitFor = function () {
                console.log("🚫 auto.waitFor被禁用 - ROOT模式下不需要无障碍服务");
                return true;
            };
            auto.service = true;
            // 完全禁用无障碍相关的权限申请
            if (auto.requestScreenCapture) auto.requestScreenCapture = function () {
                console.log("🚫 auto.requestScreenCapture被禁用");
                return true;
            };
        }
        console.log("✅ 第一阶段权限禁用完成");
    } catch (e) {
        console.log("⚠️ 第一阶段权限禁用出错: " + e.message);
    }
})();

// 第二阶段：通过global对象禁用权限申请函数
if (typeof global !== 'undefined') {
    // 禁用截图权限申请
    global.requestScreenCapture = function () {
        console.log("ROOT模式下，requestScreenCapture被禁用");
        return true;
    };

    // 禁用截图函数
    global.captureScreen = function () {
        console.log("ROOT模式下，captureScreen被禁用，请使用ROOT截图");
        return null;
    };

    // 禁用images模块的截图
    if (typeof global.images === 'undefined') {
        global.images = {};
    }
    global.images.captureScreen = function () {
        console.log("ROOT模式下，images.captureScreen被禁用，请使用ROOT截图");
        return null;
    };

    // 禁用auto模块
    if (typeof global.auto === 'undefined') {
        global.auto = {};
    }
    global.auto.waitFor = function () {
        console.log("ROOT模式下，auto.waitFor被禁用 - 不需要无障碍服务");
        return true;
    };
    global.auto.service = true; // 假装无障碍服务已开启

    // 完全禁用auto模块的权限申请
    global.auto.requestScreenCapture = function () {
        console.log("ROOT模式下，auto.requestScreenCapture被禁用");
        return true;
    };

    // 禁用images模块的其他权限申请函数
    if (typeof global.images === 'undefined') {
        global.images = {};
    }
    global.images.requestScreenCapture = function () {
        console.log("ROOT模式下，images.requestScreenCapture被禁用");
        return true;
    };
    global.images.requestScreenCaptureAsync = function () {
        console.log("ROOT模式下，images.requestScreenCaptureAsync被禁用");
        return Promise.resolve(true);
    };

    // 禁用images.read的权限检查
    if (typeof global.images.read === 'function') {
        let 原始read = global.images.read;
        global.images.read = function (path) {
            console.log("ROOT模式下，images.read跳过权限检查");
            try {
                return 原始read(path);
            } catch (e) {
                if (e.message && (e.message.includes('permission') || e.message.includes('权限'))) {
                    console.log("ROOT模式下忽略权限错误，重试读取");
                    return 原始read(path);
                }
                throw e;
            }
        };
    }

    // 重写OCR相关函数，防止自动权限申请
    if (typeof global.$ocr !== 'undefined') {
        let 原始OCR = global.$ocr;
        global.$ocr = function (img) {
            console.log("ROOT模式下调用OCR，跳过权限检查");
            return 原始OCR(img);
        };
        if (原始OCR.detect) {
            global.$ocr.detect = function (img) {
                console.log("ROOT模式下调用OCR.detect，跳过权限检查");
                return 原始OCR.detect(img);
            };
        }
    }

    // 在脚本启动时就重写$ocr函数，防止权限申请
    setTimeout(function () {
        if (typeof $ocr !== 'undefined') {
            let 原始$ocr = $ocr;
            $ocr = function (img) {
                console.log("ROOT模式下$ocr被调用，跳过权限检查");
                return 原始$ocr(img);
            };
            if (原始$ocr.detect) {
                $ocr.detect = function (img) {
                    console.log("ROOT模式下$ocr.detect被调用，跳过权限检查");
                    return 原始$ocr.detect(img);
                };
            }
            console.log("✅ $ocr函数已被安全包装");
        }
    }, 100);

    console.log("✅ 所有权限申请函数已被禁用");
}

// 引入依赖
// 在AutoJS环境中，使用全局变量而不是require
var DeviceOperation, ErrorHandler, OCR, 小红书操作;

/**
 * 统一的配置读取方法 - 所有脚本都使用这个方法
 * @param {string} 配置项名称 - 要读取的配置项名称
 * @param {any} 默认值 - 如果配置项不存在时返回的默认值
 * @returns {any} 配置项的值
 */
function 读取配置项(配置项名称, 默认值 = null) {
    try {
        var CONFIG_FILE = "/sdcard/xiaohongshu_config.json";

        if (!files.exists(CONFIG_FILE)) {
            console.log(`配置文件不存在，返回默认值: ${配置项名称} = ${默认值}`);
            return 默认值;
        }

        var 配置内容 = files.read(CONFIG_FILE);
        if (!配置内容) {
            console.log(`配置文件为空，返回默认值: ${配置项名称} = ${默认值}`);
            return 默认值;
        }

        var 配置对象 = JSON.parse(配置内容);

        if (配置对象.hasOwnProperty(配置项名称)) {
            var 配置值 = 配置对象[配置项名称];
            console.log(`读取配置: ${配置项名称} = ${配置值}`);
            return 配置值;
        }

        console.log(`配置项 "${配置项名称}" 未找到，返回默认值: ${默认值}`);
        return 默认值;
    } catch (e) {
        console.error(`读取配置项出错: ${e.message}，返回默认值: ${默认值}`);
        return 默认值;
    }
}

// 为了兼容性，保留旧的函数名
var 读取配置文件 = 读取配置项;

/**
 * 更新单个配置项
 * @param {string} 配置项名称 - 要更新的配置项名称
 * @param {any} 配置值 - 要设置的配置值
 * @returns {boolean} 是否更新成功
 */
function 更新配置项(配置项名称, 配置值) {
    try {
        var CONFIG_FILE = "/sdcard/xiaohongshu_config.json";

        // 读取现有配置
        var 现有配置 = {};
        if (files.exists(CONFIG_FILE)) {
            var 配置内容 = files.read(CONFIG_FILE);
            if (配置内容) {
                现有配置 = JSON.parse(配置内容);
            }
        }

        // 更新指定配置项
        现有配置[配置项名称] = 配置值;

        // 保存配置文件
        var 新配置内容 = JSON.stringify(现有配置, null, 2);
        files.write(CONFIG_FILE, 新配置内容);

        console.log(`✅ 配置项更新成功: ${配置项名称} = ${配置值}`);
        return true;

    } catch (e) {
        console.error(`❌ 更新配置项失败: ${e.message}`);
        return false;
    }
}

/**
 * 换号后重置行号功能
 * 根据"换号从头开始"配置决定是否重置当前操作行号
 * @returns {boolean} 是否执行了重置操作
 */
function 换号后重置行号() {
    try {
        console.log("🔄 检查是否需要重置行号...");

        // 读取"换号从头开始"配置
        var 换号从头开始 = 读取配置项("换号从头开始", true);

        if (换号从头开始) {
            console.log("✅ 检测到'换号从头开始'已启用，重置当前操作行号");

            // 只重置当前操作行号为1
            var 行号重置结果 = 更新配置项("当前操作行号", 1);

            if (行号重置结果) {
                console.log("🎯 当前操作行号已重置为1");
                if (typeof toast !== 'undefined') {
                    toast("🔄 换号重置：从第1行开始", 2000);
                }
                return true;
            } else {
                console.error("❌ 重置当前操作行号失败");
                return false;
            }
        } else {
            console.log("📋 '换号从头开始'未启用，保持当前行号");
            return false;
        }

    } catch (e) {
        console.error(`❌ 换号后重置行号出错: ${e.message}`);
        return false;
    }
}

/**
 * 换号后增加计数功能
 * 在原有的当前账号处理进度基础上增加1
 * @returns {boolean} 是否更新成功
 */
function 换号后增加账号进度计数() {
    try {
        console.log("📊 换号后增加计数...");

        // 读取当前账号处理进度
        var 当前账号处理进度 = 读取配置项("当前账号处理进度", 0);

        // 在原有基础上增加1
        var 新的账号处理进度 = 当前账号处理进度 + 1;

        // 更新配置
        var 更新结果 = 更新配置项("当前账号处理进度", 新的账号处理进度);

        if (更新结果) {
            console.log(`✅ 账号处理进度已更新: ${当前账号处理进度} → ${新的账号处理进度}`);
            if (typeof toast !== 'undefined') {
                toast(`📊 账号进度: ${新的账号处理进度}`, 2000);
            }
            return true;
        } else {
            console.error("❌ 更新账号处理进度失败");
            return false;
        }

    } catch (e) {
        console.error(`❌ 换号后增加计数出错: ${e.message}`);
        return false;
    }
}

/**
 * 转换配置值类型
 * @param {string} value - 配置文件中的字符串值
 * @param {any} 默认值 - 默认值，用于推断目标类型
 * @returns {any} 转换后的值
 */
function 转换配置值(value, 默认值) {
    if (默认值 === null || 默认值 === undefined) {
        return value; // 如果没有默认值，直接返回字符串
    }

    var 默认值类型 = typeof 默认值;

    switch (默认值类型) {
        case 'number':
            var 数字值 = Number(value);
            return isNaN(数字值) ? 默认值 : 数字值;

        case 'boolean':
            var 小写值 = value.toLowerCase();
            if (小写值 === 'true' || 小写值 === '1' || 小写值 === 'yes') {
                return true;
            } else if (小写值 === 'false' || 小写值 === '0' || 小写值 === 'no') {
                return false;
            }
            return 默认值;

        case 'string':
        default:
            return value;
    }
}

/**
 * 检查截图权限
 * @returns {boolean} 是否有截图权限
 */
function 检查截图权限() {
    console.log("检查截图权限...");
    try {
        // 如果OCR模块已加载并有该函数，则使用OCR模块的函数
        if (typeof OCR !== 'undefined' && OCR && typeof OCR.检查截图权限 === 'function') {
            console.log("使用OCR模块的检查截图权限函数");
            return OCR.检查截图权限();
        }

        // 否则使用简单的截图测试方法
        console.log("使用简单截图测试方法");

        // 检查是否为ROOT模式
        if (typeof DeviceOperation !== 'undefined') {
            let 当前模式 = DeviceOperation.获取当前模式();
            if (当前模式 === DeviceOperation.操作模式.ROOT) {
                console.log("ROOT模式下，跳过截图测试，直接返回true");
                return true;
            }
        }

        // 非ROOT模式不支持
        console.error("脚本已配置为仅支持ROOT模式");
        return false;
    } catch (e) {
        console.error("检查截图权限出错: " + e.message);
        return false;
    }
}

// 挂载到global，方便其它模块调用
if (typeof global !== 'undefined') {
    global.读取配置项 = 读取配置项;
    global.更新配置项 = 更新配置项;
    global.换号后重置行号 = 换号后重置行号;
    global.换号后增加账号进度计数 = 换号后增加账号进度计数;
}

// 如果有module.exports，也导出到模块
if (typeof module !== 'undefined' && module.exports) {
    if (!module.exports) module.exports = {};
    module.exports.检查截图权限 = 检查截图权限;
    module.exports.读取配置文件 = 读取配置文件;
    module.exports.转换配置值 = 转换配置值;
}

// 在AutoJS环境中使用require加载模块
try {
    console.log("在AutoJS环境中加载模块...");

    // 先加载DeviceOperation模块，因为其他模块可能依赖它的操作模式定义
    try {
        DeviceOperation = require('./DeviceOperation.js');
        console.log("DeviceOperation.js 加载完成");
    } catch (e) {
        try {
            DeviceOperation = require('DeviceOperation.js');
            console.log("DeviceOperation.js 加载完成（绝对路径）");
        } catch (e2) {
            console.error("DeviceOperation模块加载失败:", e.message);
            throw e;
        }
    }

    // 使用require加载模块
    var hid;
    try {
        hid = require('./hid.js');
        console.log("hid.js 加载完成");
    } catch (e) {
        try {
            hid = require('hid.js');
            console.log("hid.js 加载完成（绝对路径）");
        } catch (e2) {
            console.error("hid模块加载失败:", e.message);
            // hid模块不是必需的，可以继续
        }
    }

    // 读取 JSON 配置文件
    console.log("使用新的JSON配置文件: /sdcard/xiaohongshu_config.json");

    // 加载OCR模块
    try {
        OCR = require('./ocr.js');
        console.log("OCR.js 加载完成");
    } catch (e) {
        try {
            OCR = require('ocr.js');
            console.log("OCR.js 加载完成（绝对路径）");
        } catch (e2) {
            console.error("OCR模块加载失败:", e.message);
            // 创建空的OCR模块
            OCR = {
                获取屏幕文字信息: function () { return []; },
                点击指定关键词: function () { return false; }
            };
        }
    }

    // 修改模块加载方式，确保正确引用
    try {
        // 首先尝试相对路径加载
        小红书操作 = require('./xiaohongshu.js');
        console.log("小红书操作.js 加载完成（相对路径）");
    } catch (e) {
        console.log("相对路径加载失败，尝试绝对路径: " + e.message);
        try {
            小红书操作 = require('xiaohongshu.js');
            console.log("小红书操作.js 加载完成（绝对路径）");
        } catch (e2) {
            console.error("所有路径加载都失败:");
            console.error("相对路径错误: " + e.message);
            console.error("绝对路径错误: " + e2.message);
            throw e2; // 抛出最后一个错误
        }
    }

    try {
        // 检查模块是否成功加载
        if (小红书操作) {
            // 使用新的JSON配置文件，不需要设置API配置
            console.log("小红书操作模块将使用JSON配置文件");
            // 确保全局有 开始小红书点赞操作
            if (小红书操作 && typeof 小红书操作.开始小红书点赞操作 === 'function') {
                global.开始小红书点赞操作 = 小红书操作.开始小红书点赞操作;
                console.log("已将 开始小红书点赞操作 赋值到全局");
            } else {
                console.error("xiaohongshu.js 未正确导出 开始小红书点赞操作");
            }

            // 确保全局有 小红书点赞操作入口
            if (小红书操作 && typeof 小红书操作.小红书点赞操作入口 === 'function') {
                global.小红书点赞操作入口 = 小红书操作.小红书点赞操作入口;
                console.log("已将 小红书点赞操作入口 赋值到全局");
            } else {
                console.error("xiaohongshu.js 未正确导出 小红书点赞操作入口");
            }

            // 确保全局有 主函数
            if (小红书操作 && typeof 小红书操作.主函数 === 'function') {
                global.主函数 = 小红书操作.主函数;
                console.log("已将 主函数 赋值到全局");
            } else {
                console.error("xiaohongshu.js 未正确导出 主函数");
            }

            // 检查模块是否包含必要的函数
            if (typeof 小红书操作.获取配置项 !== 'function') {
                console.log("小红书操作模块中没有获取配置项函数，尝试其他方法获取");
                // 检查是否有其他可能的函数名
                if (typeof 小红书操作.getConfig === 'function') {
                    console.log("找到getConfig函数，创建兼容函数");
                    小红书操作.获取配置项 = 小红书操作.getConfig;
                } else if (typeof 小红书操作.获取设置 === 'function') {
                    console.log("找到获取设置函数，创建兼容函数");
                    小红书操作.获取配置项 = 小红书操作.获取设置;
                }
            }
        }
    } catch (e) {
        console.error("加载小红书操作模块出错: " + e);
        // 创建一个空的小红书操作模块，避免后续出错
        小红书操作 = {
            获取配置项: function (名称) {
                console.log("使用默认配置项: " + 名称);
                return 名称 === "操作几次恢复出厂设置" ? 4 : null;
            }
        };

        // 创建备用的全局函数，避免调用时出错
        global.小红书点赞操作入口 = function() {
            console.error("小红书操作模块加载失败，无法执行点赞操作");
            // 检查是否在AutoJS环境中
            if (typeof toast !== 'undefined') {
                toast("小红书操作模块加载失败，请检查xiaohongshu.js文件");
            }
            return false;
        };

        global.主函数 = function() {
            console.error("小红书操作模块加载失败，无法执行主函数");
            // 检查是否在AutoJS环境中
            if (typeof toast !== 'undefined') {
                toast("小红书操作模块加载失败，请检查xiaohongshu.js文件");
            }
            return false;
        };

        global.开始小红书点赞操作 = function() {
            console.error("小红书操作模块加载失败，无法执行开始小红书点赞操作");
            // 检查是否在AutoJS环境中
            if (typeof toast !== 'undefined') {
                toast("小红书操作模块加载失败，请检查xiaohongshu.js文件");
            }
            return false;
        };
    }

    try {
        ErrorHandler = require('./ErrorHandler.js');
        console.log("ErrorHandler.js 加载完成");
    } catch (e) {
        try {
            ErrorHandler = require('ErrorHandler.js');
            console.log("ErrorHandler.js 加载完成（绝对路径）");
        } catch (e2) {
            console.error("ErrorHandler模块加载失败:", e.message);
            // 创建空的ErrorHandler模块
            ErrorHandler = {
                设置错误处理配置: function () { },
                处理错误: function () { }
            };
        }
    }

    // 加载flows模块 - 兼容打包环境
    var flows模块;
    try {
        // 尝试相对路径加载
        flows模块 = require('./flows.js');
        console.log("flows.js 加载完成（相对路径）");
    } catch (e1) {
        try {
            // 尝试绝对路径加载
            flows模块 = require('flows.js');
            console.log("flows.js 加载完成（绝对路径）");
        } catch (e2) {
            try {
                // 尝试从当前目录加载
                flows模块 = require('flows');
                console.log("flows.js 加载完成（模块名）");
            } catch (e3) {
                console.error("flows模块加载失败，尝试所有方式都失败:");
                console.error("相对路径错误:", e1.message);
                console.error("绝对路径错误:", e2.message);
                console.error("模块名错误:", e3.message);

                // 创建一个空的flows模块，避免后续出错
                flows模块 = {
                    重启飞行模式: function () {
                        console.log("flows模块未加载，跳过重启飞行模式");
                        return true;
                    },
                    备份工具箱还原备份: function () {
                        console.log("flows模块未加载，跳过备份工具箱还原备份");
                        return true;
                    },
                    改机工具_阿帕奇: function () {
                        console.log("flows模块未加载，跳过改机工具_阿帕奇");
                        return true;
                    }
                };
                console.log("已创建flows模块的空实现");
            }
        }
    }

    // 确保flows变量可用
    // if (typeof flows === 'undefined') {
    //     if (typeof flows模块 === 'object') {
    //         flows = flows模块;
    //         console.log("从flows模块导入flows对象");
    //     } else if (typeof global !== 'undefined' && global.flows) {
    //         flows = global.flows;
    //         console.log("从全局变量获取flows对象");
    //     } else {
    //         console.error("无法获取flows对象");
    //     }
    // }

    // Process = require('./flows.js'); // 导入Process模块

    // var { 备份还原, 删除第一个备份, 恢复出厂设置, 重启飞行模式 } = require('./flows.js');
    // 注释掉解构导入，使用之前的方式
    // var { 开始小红书点赞操作 } = require('./xiaohongshu.js');

    console.log("所有模块加载成功");
} catch (e) {
    console.error("模块加载出错: " + e);
    // 检查是否在AutoJS环境中
    if (typeof exit !== 'undefined') {
        exit();
    } else {
        process.exit(1);
    }
}

/**
 * 全局配置参数
 */
var 全局配置 = {
    // 运行参数
    最大运行时间: 3600000,  // 最大运行时间(毫秒)，默认1小时
    默认等待时间: 2000,     // 默认等待时间(毫秒)
    调试模式: false,       // 是否开启调试模式

    // 错误处理
    最大连续错误: 5,       // 最大连续错误次数
    错误恢复策略: "返回",   // 错误恢复策略: 返回, 返回到桌面, 重启应用
    最大错误记录数: 100,    // 最大错误记录数
    最低电量: 5,           // 最低电量百分比
};

// 添加重试计数器
var 加载重试次数 = 0;
var 最大重试次数 = 10;

/**
 * 检查是否有ROOT权限
 * @returns {boolean} - 是否有ROOT权限
 */
function 检查ROOT权限() {
    console.log("检查ROOT权限...");
    try {
        // 方法1: 使用shell命令执行su -c 'id'
        let result = shell("su -c 'id'", true);
        if (result.code === 0 && result.result && result.result.indexOf("uid=0") !== -1) {
            console.log("检测到ROOT权限(方法1)");
            return true;
        }

        // 方法2: 尝试写入system目录
        result = shell("su -c 'touch /system/test_root && rm /system/test_root'", true);
        if (result.code === 0) {
            console.log("检测到ROOT权限(方法2)");
            return true;
        }

        // 方法3: 尝试执行简单ROOT命令
        try {
            let cmd = "su -c 'echo success'";
            let output = shell(cmd, true).result.trim();
            if (output === "success") {
                console.log("检测到ROOT权限(方法3)");
                return true;
            }
        } catch (e) {
            console.log("方法3检测ROOT失败: " + e.message);
        }

        // 方法4: 如果DeviceOperation模块已加载，使用其方法检测
        if (typeof DeviceOperation !== 'undefined' && typeof DeviceOperation.检查ROOT权限 === 'function') {
            let hasRoot = DeviceOperation.检查ROOT权限();
            if (hasRoot) {
                console.log("检测到ROOT权限(方法4)");
                return true;
            }
        }

        console.log("未检测到ROOT权限");
        return false;
    } catch (e) {
        console.error("检查ROOT权限出错: " + e.message);
        return false;
    }
}

/**
 * 使用ROOT权限开启截图权限
 * @returns {boolean} - 是否成功开启
 */
function 使用ROOT开启截图权限() {
    console.log("使用ROOT权限开启截图权限...");
    try {
        // 首次尝试获取root权限
        console.log("首次尝试获取root权限...");
        let result = shell("su -c 'echo success'", true);
        if (result.code === 0 && result.result.trim() === "success") {
            console.log("成功获取root权限，标记为已授权");
        } else {
            console.log("首次ROOT授权可能失败，继续尝试其他方法");
        }

        // 方法1: 尝试模拟用户点击授权按钮 - 多个位置尝试点击
        let 点击坐标集 = [
            [900, 1200],  // 中下部常见授权按钮位置
            [950, 1600],  // 底部常见授权按钮位置
            [550, 1700],  // 底部中间可能的授权按钮
            [800, 1900]   // 底部右侧常见授权按钮位置
        ];

        for (let 坐标 of 点击坐标集) {
            console.log(`尝试点击可能的授权按钮位置: (${坐标[0]}, ${坐标[1]})`);
            shell(`su -c 'input tap ${坐标[0]} ${坐标[1]}'`, true);
            sleep(500);  // 短暂等待响应
        }

        // 方法2: 使用ADB命令直接授权(适用于部分设备)
        shell("su -c 'settings put secure android_id 1'", true);

        // 方法3: 通过媒体投影权限授权(适用于部分设备)
        shell("su -c 'settings put global hidden_api_policy 1'", true);
        shell("su -c 'settings put global hidden_api_policy_pre_p_apps 1'", true);
        shell("su -c 'settings put global hidden_api_policy_p_apps 1'", true);

        // 等待权限生效
        sleep(1500);

        // 检查是否已获取权限
        let 权限检查次数 = 0;
        let 最大检查次数 = 3;
        let 已获取权限 = false;

        while (权限检查次数 < 最大检查次数 && !已获取权限) {
            权限检查次数++;
            console.log(`第${权限检查次数}次检查截图权限...`);

            if (typeof OCR !== 'undefined' && typeof OCR.检查截图权限 === 'function') {
                已获取权限 = OCR.检查截图权限();
                if (已获取权限) {
                    console.log("使用ROOT成功开启截图权限");
                    return true;
                }
            } else {
                // 如果没有检查函数，尝试直接创建截图
                try {
                    // ROOT模式下直接返回成功
                    if (typeof DeviceOperation !== 'undefined') {
                        let 当前模式 = DeviceOperation.获取当前模式();
                        if (当前模式 === DeviceOperation.操作模式.ROOT) {
                            console.log("ROOT模式下，跳过截图测试，直接返回成功");
                            已获取权限 = true;
                            return true;
                        }
                    }

                    // 非ROOT模式不支持
                    console.error("脚本已配置为仅支持ROOT模式");
                    return false;
                } catch (e) {
                    console.log("尝试创建截图失败: " + e.message);
                }
            }

            if (!已获取权限 && 权限检查次数 < 最大检查次数) {
                console.log("截图权限检查失败，继续尝试点击授权");
                // 再次尝试点击不同位置
                let x = 800 + Math.floor(Math.random() * 200);
                let y = 1500 + Math.floor(Math.random() * 400);
                shell(`su -c 'input tap ${x} ${y}'`, true);
                sleep(1000);
            }
        }

        console.log("使用ROOT开启截图权限失败");
        return false;
    } catch (e) {
        console.error("使用ROOT开启截图权限出错: " + e.message);
        return false;
    }
}

/**
 * 主函数
 */
function main() {
    console.log("======== 小红书自动点赞脚本启动 ========");
    if (typeof device !== 'undefined') {
        console.log("操作系统: " + device.brand + " " + device.model + ", Android " + device.release);
        console.log("屏幕分辨率: " + device.width + "x" + device.height);
    }

    // 检查模块加载状态
    console.log("检查模块加载状态：");
    console.log("DeviceOperation: " + (typeof DeviceOperation !== 'undefined' ? "已加载" : "未加载"));
    console.log("OCR: " + (typeof OCR !== 'undefined' ? "已加载" : "未加载"));
    console.log("ErrorHandler: " + (typeof ErrorHandler !== 'undefined' ? "已加载" : "未加载"));
    // console.log("flows: " + (typeof flows !== 'undefined' ? "已加载" : "未加载"));

    // 检查ROOT权限
    let 有ROOT权限 = 检查ROOT权限();
    console.log("ROOT权限检查结果: " + (有ROOT权限 ? "已获取" : "未获取"));

    // ROOT模式专用 - 完全跳过无障碍服务和权限申请
    if (typeof DeviceOperation !== 'undefined') {
        var 当前模式 = DeviceOperation.获取当前模式();

        if (当前模式 === DeviceOperation.操作模式.ROOT) {
            console.log("ROOT模式下，完全跳过无障碍服务和所有权限申请");
        } else {
            console.log("❌ 脚本已配置为仅支持ROOT模式");
            if (typeof toast !== 'undefined') {
                toast("脚本已配置为仅支持ROOT模式，请检查配置");
            }
            return false;
        }

        // ROOT模式下只测试ROOT功能
        try {
            console.log("测试ROOT截图功能...");
            let testResult = shell("su -c 'screencap -p /sdcard/test_root_screenshot.png'", true);
            if (testResult.code === 0) {
                console.log("✅ ROOT截图测试成功");
                shell("rm /sdcard/test_root_screenshot.png", true);
                console.log("✅ ROOT模式准备完成，无需任何权限申请");
            } else {
                console.log("❌ ROOT截图测试失败，请检查ROOT权限");
                console.log("错误输出: " + testResult.error);
            }
        } catch (e) {
            console.log("❌ ROOT模式测试出错: " + e.message);
        }
        // ROOT模式专用，无需任何权限申请

        // 显示当前操作模式
        var 当前模式 = DeviceOperation.获取当前模式();
        var 交互模式 = DeviceOperation.获取交互操作模式();
        var 窗口信息模式 = DeviceOperation.获取窗口信息模式();

        console.log("当前操作模式: " + (当前模式 === DeviceOperation.操作模式.无障碍 ? "无障碍" :
            当前模式 === DeviceOperation.操作模式.HID ? "HID" :
                当前模式 === DeviceOperation.操作模式.ROOT ? "ROOT" : "未知"));

        console.log("当前交互操作模式: " + (交互模式 === DeviceOperation.操作模式.无障碍 ? "无障碍" :
            交互模式 === DeviceOperation.操作模式.HID ? "HID" :
                交互模式 === DeviceOperation.操作模式.ROOT ? "ROOT" : "未知"));

        console.log("当前窗口信息模式: " + (窗口信息模式 === DeviceOperation.操作模式.无障碍 ? "无障碍" :
            窗口信息模式 === DeviceOperation.操作模式.ROOT ? "ROOT" : "未知"));
    } else {
        console.error("DeviceOperation模块未加载，无法申请权限");
    }

    // 设置错误处理
    if (typeof ErrorHandler !== 'undefined' && typeof ErrorHandler.设置错误处理配置 === 'function') {
        ErrorHandler.设置错误处理配置({
            最大连续错误: 全局配置.最大连续错误,
            最大错误记录数: 全局配置.最大错误记录数,
            错误恢复策略: 全局配置.错误恢复策略
        });
    }

    // 启动垃圾回收定时器
    启动垃圾回收定时器();

    console.log("初始化完成，开始执行操作流程");

    // 启动整个操作流程
    整个操作流程();
}

/**
 * 定期执行垃圾回收
 */
function 启动垃圾回收定时器() {
    // 每15分钟强制执行一次垃圾回收
    setInterval(function () {
        console.log("执行垃圾回收...");
        try {
            // 使用Java系统垃圾回收（适用于AutoJS 6）
            java.lang.System.gc();
            console.log("垃圾回收完成");
        } catch (e) {
            console.error("垃圾回收失败: " + e.message);
        }
    }, 15 * 60 * 1000);

    // 注册exit事件，在脚本退出时执行清理
    if (typeof events !== 'undefined' && events.on) {
        events.on('exit', function () {
            console.log("脚本即将退出，执行最终清理工作");
            try {
                java.lang.System.gc();
                console.log("退出前垃圾回收完成");
            } catch (e) {
                console.error("退出前垃圾回收失败: " + e.message);
            }
        });
        console.log("已注册exit事件处理器");
    }
}

/**
 * 整个操作流程 - 根据配置项循环执行还原备份和小红书点赞操作
 */
function 整个操作流程() {
    console.log("开始执行整个操作流程");
    
    // 设置默认值
    var 操作几次恢复出厂设置 = 1; // 默认值
    
    console.log("将执行 " + 操作几次恢复出厂设置 + " 次还原备份和点赞操作，然后执行恢复出厂设置");
    // 当前执行次数
    var 当前执行次数 = 0;
    //重启飞行模式();
    //备份还原();
    //删除第一个备份();
    //开始小红书点赞操作();
    小红书点赞操作入口()
            

    //开始小红书点赞操作();
    // do {
    //     while(当前执行次数 < 操作几次恢复出厂设置) {
    //         重启飞行模式();
    //         备份还原();
    //         删除第一个备份();
    //         开始小红书点赞操作();
    //         当前执行次数++;
    //         // 从全局变量获取配置
    //         if (typeof global.操作几次恢复出厂设置 !== 'undefined' && global.操作几次恢复出厂设置 !== null) {
    //             操作几次恢复出厂设置 = global.操作几次恢复出厂设置;
    //             console.log("从全局变量获取配置: 操作几次恢复出厂设置 = " + 操作几次恢复出厂设置);
    //         }
    //     }
        
    //     恢复出厂设置();
        
    // } while(true);
}

// 执行主函数
setTimeout(function () {
    console.log("延迟执行主函数，确保模块加载完成...");
    main();
}, 1000);