
📊 ========== 操作结果汇总 ==========
04:27:35.338/D: ✅ 操作成功: true
04:27:35.339/D: ❤️ 点赞状态: 已完成 (本次成功)
04:27:35.340/D: ⭐ 收藏状态: 已完成 (非本次)
04:27:35.341/D: 📈 当前数量: 点赞=147, 收藏=1555
04:27:35.342/D: 🎯 达标状态: 点赞=未达标, 收藏=未达标
04:27:35.344/D: =====================================

04:27:35.345/D: 操作结果: 点赞成功, 已收藏，跳过
04:27:35.357/D: 提交到服务端的操作前数量: 点赞=147, 收藏=1555
04:27:35.361/D: 发送POST请求到: http://xhss.ke999.cn/update-status
04:27:35.365/D: 请求数据: {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":191,"status":"success","operation_type":"both","before_like_count":147,"before_collect_count":1555}
04:27:35.566/E: 第1次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":191,"status":"success","operation_type":"both","before_like_count":147,"before_collect_count":1555} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":191,"status":"success","operation_type":"both","before_like_count":147,"before_collect_count":1555} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:27:35.569/D: 第2次尝试发送请求...
04:27:35.570/D: 等待 2000 毫秒
04:27:37.593/E: 第2次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":191,"status":"success","operation_type":"both","before_like_count":147,"before_collect_count":1555} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":191,"status":"success","operation_type":"both","before_like_count":147,"before_collect_count":1555} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:27:37.595/D: 第3次尝试发送请求...
04:27:37.596/D: 等待 2000 毫秒
04:27:39.624/E: 第3次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":191,"status":"success","operation_type":"both","before_like_count":147,"before_collect_count":1555} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":191,"status":"success","operation_type":"both","before_like_count":147,"before_collect_count":1555} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:27:39.625/E: 所有重试都失败，返回最终错误
04:27:39.627/E: 错误堆栈: 	at file:/storage/emulated/0/脚本/xiaohongshu.js:162 (发送请求)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:501 (更新操作状态)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:1739 (执行自动互动)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:4455 (小红书点赞操作入口)
	at $remote/main不自动重启.js:932 (整个操作流程)
	at $remote/main不自动重启.js:882 (main)
	at $remote/main不自动重启.js:958

04:27:39.628/D: 更新状态结果: {"success":false,"message":"发送请求出错: 方法 \"http.post\" 调用失败. Failed to coerce {\"username\":\"admin\",\"device_token\":\"device_1753907162823_qfv3cn55\",\"link_id\":191,\"status\":\"success\",\"operation_type\":\"both\",\"before_like_count\":147,\"before_collect_count\":1555} (String) into a JavaScript object.\njava.lang.IllegalArgumentException: Failed to coerce {\"username\":\"admin\",\"device_token\":\"device_1753907162823_qfv3cn55\",\"link_id\":191,\"status\":\"success\",\"operation_type\":\"both\",\"before_like_count\":147,\"before_collect_count\":1555} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)","error_stack":"\tat file:/storage/emulated/0/脚本/xiaohongshu.js:162 (发送请求)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:501 (更新操作状态)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:1739 (执行自动互动)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:4455 (小红书点赞操作入口)\n\tat $remote/main不自动重启.js:932 (整个操作流程)\n\tat $remote/main不自动重启.js:882 (main)\n\tat $remote/main不自动重启.js:958\n"}


🎉 ========== 提前完成：所有任务已完成 ==========
04:28:48.410/D: ✅ 当前页面已满足所有操作要求，无需执行匹配流程和额外操作
04:28:48.410/D: ⚡ 节省时间，直接返回成功结果
04:28:48.411/D: 操作结果: 已点赞，跳过, 已收藏，跳过
04:28:48.411/D: 提交到服务端的操作前数量: 点赞=133, 收藏=1538
04:28:48.412/D: 发送POST请求到: http://xhss.ke999.cn/update-status
04:28:48.412/D: 请求数据: {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538}
04:28:48.567/E: 第1次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:28:48.568/D: 第2次尝试发送请求...
04:28:48.569/D: 等待 2000 毫秒
04:28:50.581/E: 第2次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:28:50.582/D: 第3次尝试发送请求...
04:28:50.582/D: 等待 2000 毫秒
04:28:52.592/E: 第3次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:28:52.593/E: 所有重试都失败，返回最终错误
04:28:52.594/E: 错误堆栈: 	at file:/storage/emulated/0/脚本/xiaohongshu.js:162 (发送请求)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:501 (更新操作状态)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:1739 (执行自动互动)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:4455 (小红书点赞操作入口)
	at $remote/main不自动重启.js:932 (整个操作流程)
	at $remote/main不自动重启.js:882 (main)
	at $remote/main不自动重启.js:958

04:28:52.595/D: 更新状态结果: {"success":false,"message":"发送请求出错: 方法 \"http.post\" 调用失败. Failed to coerce {\"username\":\"admin\",\"device_token\":\"device_1753907162823_qfv3cn55\",\"link_id\":190,\"status\":\"success\",\"operation_type\":\"both\",\"before_like_count\":133,\"before_collect_count\":1538} (String) into a JavaScript object.\njava.lang.IllegalArgumentException: Failed to coerce {\"username\":\"admin\",\"device_token\":\"device_1753907162823_qfv3cn55\",\"link_id\":190,\"status\":\"success\",\"operation_type\":\"both\",\"before_like_count\":133,\"before_collect_count\":1538} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)","error_stack":"\tat file:/storage/emulated/0/脚本/xiaohongshu.js:162 (发送请求)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:501 (更新操作状态)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:1739 (执行自动互动)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:4455 (小红书点赞操作入口)\n\tat $remote/main不自动重启.js:932 (整个操作流程)\n\tat $remote/main不自动重启.js:882 (main)\n\tat $remote/main不自动重启.js:958\n"}

🎉 ========== 提前完成：所有任务已完成 ==========
04:29:10.671/D: ✅ 当前页面已满足所有操作要求，无需执行匹配流程和额外操作
04:29:10.671/D: ⚡ 节省时间，直接返回成功结果
04:29:10.672/D: 操作结果: 已点赞，跳过, 已收藏，跳过
04:29:10.672/D: 提交到服务端的操作前数量: 点赞=133, 收藏=1538
04:29:10.673/D: 发送POST请求到: http://xhss.ke999.cn/update-status
04:29:10.674/D: 请求数据: {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538}
04:29:10.682/E: 第1次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:29:10.682/D: 第2次尝试发送请求...
04:29:10.683/D: 等待 2000 毫秒
04:29:12.693/E: 第2次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:29:12.694/D: 第3次尝试发送请求...
04:29:12.695/D: 等待 2000 毫秒
04:29:14.704/E: 第3次请求出错: 方法 "http.post" 调用失败. Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object.
java.lang.IllegalArgumentException: Failed to coerce {"username":"admin","device_token":"device_1753907162823_qfv3cn55","link_id":190,"status":"success","operation_type":"both","before_like_count":133,"before_collect_count":1538} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)
04:29:14.705/E: 所有重试都失败，返回最终错误
04:29:14.706/E: 错误堆栈: 	at file:/storage/emulated/0/脚本/xiaohongshu.js:162 (发送请求)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:501 (更新操作状态)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:1739 (执行自动互动)
	at file:/storage/emulated/0/脚本/xiaohongshu.js:4455 (小红书点赞操作入口)
	at $remote/main不自动重启.js:932 (整个操作流程)
	at $remote/main不自动重启.js:882 (main)
	at $remote/main不自动重启.js:958

04:29:14.708/D: 更新状态结果: {"success":false,"message":"发送请求出错: 方法 \"http.post\" 调用失败. Failed to coerce {\"username\":\"admin\",\"device_token\":\"device_1753907162823_qfv3cn55\",\"link_id\":190,\"status\":\"success\",\"operation_type\":\"both\",\"before_like_count\":133,\"before_collect_count\":1538} (String) into a JavaScript object.\njava.lang.IllegalArgumentException: Failed to coerce {\"username\":\"admin\",\"device_token\":\"device_1753907162823_qfv3cn55\",\"link_id\":190,\"status\":\"success\",\"operation_type\":\"both\",\"before_like_count\":133,\"before_collect_count\":1538} (String) into a JavaScript object (file:/storage/emulated/0/脚本/xiaohongshu.js#162)","error_stack":"\tat file:/storage/emulated/0/脚本/xiaohongshu.js:162 (发送请求)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:501 (更新操作状态)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:1739 (执行自动互动)\n\tat file:/storage/emulated/0/脚本/xiaohongshu.js:4455 (小红书点赞操作入口)\n\tat $remote/main不自动重启.js:932 (整个操作流程)\n\tat $remote/main不自动重启.js:882 (main)\n\tat $remote/main不自动重启.js:958\n"}
