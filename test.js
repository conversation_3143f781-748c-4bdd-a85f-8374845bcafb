/**
 * 独立测试页面信息获取功能
 * 作者: Claude
 * 日期: 2025-07-19
 */

console.log("🧪 开始测试页面信息获取功能");

// 引入DeviceOperation模块
var DeviceOperation;
try {
    DeviceOperation = require('./DeviceOperation.js');
    console.log("✅ DeviceOperation模块加载成功");
} catch (e) {
    console.error("❌ DeviceOperation模块加载失败:", e.message);
    exit();
}

/**
 * 获取窗口XML - 简化版本
 */
function 获取窗口XML() {
    try {
        console.log("🔍 开始获取界面XML...");
        let pageXml = DeviceOperation.获取窗口XML();

        if (pageXml && pageXml.length > 0) {
            console.log(`✅ 成功获取XML，大小: ${pageXml.length} 字节`);
            console.log(pageXml);
            return pageXml;
        } else {
            console.log("❌ XML获取失败或为空");
            return null;
        }
    } catch (e) {
        console.error("❌ 获取XML出错:", e.message);
        return null;
    }
}

/**
 * 分析XML中的节点信息
 */
function 分析XML节点(pageXml) {
    console.log("🔍 开始分析XML节点信息...");

    // 1. 查找所有包含content-desc的节点
    let 所有节点 = pageXml.match(/<node[^>]*>/g);
    if (所有节点) {
        console.log(`📊 找到 ${所有节点.length} 个节点`);

        // 查找收藏相关节点
        let 收藏节点 = 所有节点.filter(node => node.includes('收藏'));
        console.log(`🔍 找到 ${收藏节点.length} 个收藏相关节点:`);
        收藏节点.forEach((node, index) => {
            console.log(`  ${index + 1}. ${node}`);
        });

        // 查找点赞相关节点
        let 点赞节点 = 所有节点.filter(node => node.includes('点赞'));
        console.log(`🔍 找到 ${点赞节点.length} 个点赞相关节点:`);
        点赞节点.forEach((node, index) => {
            console.log(`  ${index + 1}. ${node}`);
        });

        // 查找包含bounds的节点
        let bounds节点 = 所有节点.filter(node => node.includes('bounds='));
        console.log(`🔍 找到 ${bounds节点.length} 个包含bounds的节点`);

        // 查找同时包含收藏和bounds的节点
        let 收藏bounds节点 = 所有节点.filter(node => node.includes('收藏') && node.includes('bounds='));
        console.log(`🔍 找到 ${收藏bounds节点.length} 个同时包含收藏和bounds的节点:`);
        收藏bounds节点.forEach((node, index) => {
            console.log(`  ${index + 1}. ${node}`);
        });

        // 查找同时包含点赞和bounds的节点
        let 点赞bounds节点 = 所有节点.filter(node => node.includes('点赞') && node.includes('bounds='));
        console.log(`🔍 找到 ${点赞bounds节点.length} 个同时包含点赞和bounds的节点:`);
        点赞bounds节点.forEach((node, index) => {
            console.log(`  ${index + 1}. ${node}`);
        });

    } else {
        console.log("❌ 未找到任何节点");
    }
}

/**
 * 测试正则表达式匹配
 */
function 测试正则匹配(pageXml) {
    console.log("🔍 开始测试正则表达式匹配...");

    // 测试收藏匹配
    console.log("📝 测试收藏匹配:");

    // 方法1：查找content-desc中的收藏信息
    let 收藏desc匹配 = pageXml.match(/content-desc="[^"]*收藏[^"]*"/g);
    if (收藏desc匹配) {
        console.log(`  找到 ${收藏desc匹配.length} 个收藏content-desc:`);
        收藏desc匹配.forEach((match, index) => {
            console.log(`    ${index + 1}. ${match}`);
        });
    } else {
        console.log("  ❌ 未找到收藏content-desc");
    }

    // 方法2：查找完整的收藏节点
    let 收藏节点匹配 = pageXml.match(/<node[^>]*content-desc="[^"]*收藏[^"]*"[^>]*>/g);
    if (收藏节点匹配) {
        console.log(`  找到 ${收藏节点匹配.length} 个完整收藏节点:`);
        收藏节点匹配.forEach((match, index) => {
            console.log(`    ${index + 1}. ${match.substring(0, 150)}${match.length > 150 ? '...' : ''}`);

            // 检查是否包含bounds
            if (match.includes('bounds=')) {
                let bounds = match.match(/bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
                if (bounds) {
                    console.log(`      ✅ 包含bounds: [${bounds[1]},${bounds[2]}][${bounds[3]},${bounds[4]}]`);
                } else {
                    console.log(`      ❌ bounds格式不正确`);
                }
            } else {
                console.log(`      ❌ 不包含bounds信息`);
            }
        });
    } else {
        console.log("  ❌ 未找到完整收藏节点");
    }

    // 测试点赞匹配
    console.log("📝 测试点赞匹配:");

    // 方法1：查找content-desc中的点赞信息
    let 点赞desc匹配 = pageXml.match(/content-desc="[^"]*点赞[^"]*"/g);
    if (点赞desc匹配) {
        console.log(`  找到 ${点赞desc匹配.length} 个点赞content-desc:`);
        点赞desc匹配.forEach((match, index) => {
            console.log(`    ${index + 1}. ${match}`);
        });
    } else {
        console.log("  ❌ 未找到点赞content-desc");
    }

    // 方法2：查找完整的点赞节点
    let 点赞节点匹配 = pageXml.match(/<node[^>]*content-desc="[^"]*点赞[^"]*"[^>]*>/g);
    if (点赞节点匹配) {
        console.log(`  找到 ${点赞节点匹配.length} 个完整点赞节点:`);
        点赞节点匹配.forEach((match, index) => {
            console.log(`    ${index + 1}. ${match.substring(0, 150)}${match.length > 150 ? '...' : ''}`);

            // 检查是否包含bounds
            if (match.includes('bounds=')) {
                let bounds = match.match(/bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
                if (bounds) {
                    console.log(`      ✅ 包含bounds: [${bounds[1]},${bounds[2]}][${bounds[3]},${bounds[4]}]`);
                } else {
                    console.log(`      ❌ bounds格式不正确`);
                }
            } else {
                console.log(`      ❌ 不包含bounds信息`);
            }
        });
    } else {
        console.log("  ❌ 未找到完整点赞节点");
    }
}

/**
 * 完整的页面信息获取函数 - 与xiaohongshu.js中的使用ROOT获取页面信息()完全一致
 */
function 使用ROOT获取页面信息() {
    try {
        // 获取当前窗口XML
        let pageXml = 获取窗口XML();
        if (!pageXml) {
            console.error("获取页面XML失败");
            return null;
        }

        console.log("成功获取窗口XML，开始解析页面信息");

        // 初始化返回结果
        let 页面信息 = {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            评论数: null,
            已点赞: false,
            已收藏: false,
            是否视频: false,
            内容类型: "图文",
            最长文本列表: [], // 最长文本列表字段
            点赞按钮坐标: null,
            收藏按钮坐标: null
        };

        // 1. 解析用户名 - 多种方式查找
        console.log("🔍 开始提取用户名...");

        // 方式1: 查找包含"作者"的content-desc属性
        let 作者匹配 = pageXml.match(/content-desc="作者([^"]+)"/);
        if (作者匹配) {
            页面信息.用户名 = 作者匹配[1].trim();
            console.log(`✅ 找到用户名(方式1-作者前缀): ${页面信息.用户名}`);
        }

        // 方式2: 查找带有"作者"前缀但逗号分隔的情况
        if (!页面信息.用户名) {
            let 作者逗号匹配 = pageXml.match(/content-desc="作者,([^"]+)"/);
            if (作者逗号匹配) {
                页面信息.用户名 = 作者逗号匹配[1].trim();
                console.log(`✅ 找到用户名(方式2-作者逗号): ${页面信息.用户名}`);
            }
        }

        // 如果仍然没有找到用户名，输出警告
        if (!页面信息.用户名) {
            console.warn("⚠️ 未能提取到用户名");
        }

        // 2. 收集所有可能的文本内容
        console.log("🔍 开始提取文本内容...");
        let 所有文本内容 = [];

        // 2.1 提取所有text属性中的文本
        const textPattern = /text=['"]([^'"]+)['"]/g;
        let textMatch;
        while ((textMatch = textPattern.exec(pageXml)) !== null) {
            let 文本 = textMatch[1].trim();
            if (文本.length > 5) { // 只收集长度大于5的文本
                // 过滤掉明显是功能按钮的文本
                if (!文本.match(/^(关注|点赞|收藏|评论|分享|举报|送礼物|说点什么)/) && 文本 !== "截屏分享至") {
                    所有文本内容.push(文本);
                    console.log(`📝 从text属性提取: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""}`);
                }
            }
        }

        // 2.2 提取所有content-desc属性中的文本
        const descPattern = /content-desc=['"]([^'"]+)['"]/g;
        let descMatch;
        while ((descMatch = descPattern.exec(pageXml)) !== null) {
            let 文本 = descMatch[1].trim();
            if (文本.length > 5 && !文本.startsWith("作者,") && !文本.startsWith("点赞") && !文本.startsWith("收藏") && !文本.startsWith("评论") && 文本 !== "截屏分享至") {
                所有文本内容.push(文本);
                console.log(`📝 从content-desc提取: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""}`);
            }
        }

        // 3. 对文本内容进行处理和排序
        // 3.1 过滤掉重复内容
        let 唯一文本内容 = 所有文本内容.filter((value, index, self) => {
            return self.indexOf(value) === index;
        });

        // 3.2 按长度排序
        唯一文本内容.sort((a, b) => b.length - a.length);

        // 3.3 取最长的3段文本
        页面信息.最长文本列表 = 唯一文本内容.slice(0, 3);

        // 输出调试信息
        console.log(`📊 共提取到 ${唯一文本内容.length} 段文本内容`);
        for (let i = 0; i < 页面信息.最长文本列表.length; i++) {
            let 文本 = 页面信息.最长文本列表[i];
            let 显示文本 = 文本.length > 100 ? 文本.substring(0, 100) + "..." : 文本;
            console.log(`📄 长文本${i + 1} (${文本.length}字): ${显示文本}`);
        }

        // 4. 为了兼容性，将最长文本设置为标题和内容
        if (页面信息.最长文本列表.length > 0) {
            页面信息.标题 = 页面信息.最长文本列表[0];
            if (页面信息.最长文本列表.length > 1) {
                页面信息.内容 = 页面信息.最长文本列表[1];
            }
        }

        // 5. 解析点赞信息和坐标
        console.log("🔍 开始查找点赞按钮信息...");
        let 点赞节点匹配 = pageXml.match(/<node[^>]*content-desc="(已点赞|点赞)(?:\s+)?(\d*)"[^>]*>/);
        if (点赞节点匹配) {
            let 完整节点 = 点赞节点匹配[0];
            console.log(`🔍 找到点赞节点: ${完整节点.substring(0, 100)}...`);

            // 从完整节点中提取bounds信息
            let bounds匹配 = 完整节点.match(/bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
            if (bounds匹配) {
                let 状态 = 点赞节点匹配[1];
                let 数量文本 = 点赞节点匹配[2];

                页面信息.已点赞 = 状态 === "已点赞";
                页面信息.点赞数 = 数量文本 ? parseInt(数量文本) : 0;

                let 点赞左 = parseInt(bounds匹配[1]);
                let 点赞上 = parseInt(bounds匹配[2]);
                let 点赞右 = parseInt(bounds匹配[3]);
                let 点赞下 = parseInt(bounds匹配[4]);

                页面信息.点赞按钮坐标 = {
                    左: 点赞左, 上: 点赞上, 右: 点赞右, 下: 点赞下,
                    中心X: Math.round((点赞左 + 点赞右) / 2),
                    中心Y: Math.round((点赞上 + 点赞下) / 2)
                };

                console.log(`✅ 点赞信息: ${页面信息.已点赞 ? '已点赞' : '未点赞'}, 数量=${页面信息.点赞数}`);
                console.log(`✅ 点赞按钮坐标: [${点赞左},${点赞上}][${点赞右},${点赞下}] 中心点: (${页面信息.点赞按钮坐标.中心X}, ${页面信息.点赞按钮坐标.中心Y})`);
            } else {
                console.log("❌ 找到点赞content-desc但未找到bounds信息");
            }
        } else {
            console.log("❌ 未找到点赞按钮信息");
        }

        // 6. 解析收藏信息和坐标
        console.log("🔍 开始查找收藏按钮信息...");
        let 收藏节点匹配 = pageXml.match(/<node[^>]*content-desc="(已收藏|收藏)(?:\s+)?(\d*)"[^>]*>/);
        if (收藏节点匹配) {
            let 完整节点 = 收藏节点匹配[0];
            console.log(`🔍 找到收藏节点: ${完整节点.substring(0, 100)}...`);

            // 从完整节点中提取bounds信息
            let bounds匹配 = 完整节点.match(/bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
            if (bounds匹配) {
                let 状态 = 收藏节点匹配[1];
                let 数量文本 = 收藏节点匹配[2];

                页面信息.已收藏 = 状态 === "已收藏";
                页面信息.收藏数 = 数量文本 ? parseInt(数量文本) : 0;

                let 收藏左 = parseInt(bounds匹配[1]);
                let 收藏上 = parseInt(bounds匹配[2]);
                let 收藏右 = parseInt(bounds匹配[3]);
                let 收藏下 = parseInt(bounds匹配[4]);

                页面信息.收藏按钮坐标 = {
                    左: 收藏左, 上: 收藏上, 右: 收藏右, 下: 收藏下,
                    中心X: Math.round((收藏左 + 收藏右) / 2),
                    中心Y: Math.round((收藏上 + 收藏下) / 2)
                };

                console.log(`✅ 收藏信息: ${页面信息.已收藏 ? '已收藏' : '未收藏'}, 数量=${页面信息.收藏数}`);
                console.log(`✅ 收藏按钮坐标: [${收藏左},${收藏上}][${收藏右},${收藏下}] 中心点: (${页面信息.收藏按钮坐标.中心X}, ${页面信息.收藏按钮坐标.中心Y})`);
            } else {
                console.log("❌ 找到收藏content-desc但未找到bounds信息");
            }
        } else {
            console.log("❌ 未找到收藏按钮信息");
        }

        // 7. 解析评论数
        console.log("🔍 开始查找评论信息...");
        let 评论匹配 = pageXml.match(/content-desc="评论\s*(\d*)"/);
        if (评论匹配) {
            页面信息.评论数 = 评论匹配[1] ? parseInt(评论匹配[1]) : 0;
            console.log(`✅ 评论数: ${页面信息.评论数}`);
        } else {
            console.log("❌ 未找到评论信息");
        }

        console.log("🎉 页面信息解析完成");
        return 页面信息;

    } catch (e) {
        console.error("❌ 获取页面信息出错:", e.message);
        console.error("错误堆栈:", e.stack);
        return null;
    }
}

/**
 * 主测试函数
 */
function 主测试() {
    console.log("🚀 开始完整页面信息获取测试");
    console.log("=" .repeat(60));

    // 调用完整的页面信息获取函数
    let 页面信息 = 使用ROOT获取页面信息();

    if (页面信息) {
        console.log("");
        console.log("=" .repeat(60));
        console.log("📋 页面信息获取结果总结:");
        console.log("=" .repeat(60));

        console.log(`👤 用户名: ${页面信息.用户名 || '未获取到'}`);
        console.log(`📄 标题: ${页面信息.标题 ? 页面信息.标题.substring(0, 50) + (页面信息.标题.length > 50 ? '...' : '') : '未获取到'}`);
        console.log(`📝 内容: ${页面信息.内容 ? 页面信息.内容.substring(0, 50) + (页面信息.内容.length > 50 ? '...' : '') : '未获取到'}`);
        console.log(`📊 文本段数: ${页面信息.最长文本列表.length}`);

        console.log(`👍 点赞: ${页面信息.已点赞 ? '已点赞' : '未点赞'} (${页面信息.点赞数 || 0})`);
        console.log(`⭐ 收藏: ${页面信息.已收藏 ? '已收藏' : '未收藏'} (${页面信息.收藏数 || 0})`);
        console.log(`💬 评论: ${页面信息.评论数 || 0}`);

        if (页面信息.点赞按钮坐标) {
            let 坐标 = 页面信息.点赞按钮坐标;
            console.log(`📍 点赞按钮坐标: 中心点(${坐标.中心X}, ${坐标.中心Y})`);
        } else {
            console.log(`📍 点赞按钮坐标: 未获取到`);
        }

        if (页面信息.收藏按钮坐标) {
            let 坐标 = 页面信息.收藏按钮坐标;
            console.log(`📍 收藏按钮坐标: 中心点(${坐标.中心X}, ${坐标.中心Y})`);
        } else {
            console.log(`📍 收藏按钮坐标: 未获取到`);
        }

        console.log("=" .repeat(60));
        console.log("✅ 页面信息获取测试完成 - 可以直接移植到xiaohongshu.js");

    } else {
        console.log("❌ 页面信息获取失败");
    }

    // 保存XML到文件用于进一步分析
    try {
        files.write('/sdcard/debug_xml.txt', pageXml);
        console.log("📄 XML已保存到 /sdcard/debug_xml.txt");
    } catch (e) {
        console.log("⚠️ 无法保存XML文件:", e.message);
    }
}

// 执行测试
console.log("⏰ 3秒后开始测试，请确保已打开小红书文章页面...");
setTimeout(function() {
    主测试();
}, 3000);