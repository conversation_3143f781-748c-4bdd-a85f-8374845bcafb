18:34:27.307/V: 开始运行 [$remote/xhs root直接跳转服务端版.js].
18:34:27.414/D: 小红书API模块已加载
18:34:27.415/D: AutoJS环境检测到，启动主函数...
18:34:27.415/D: === 小红书自动互动工具 - 服务端版本 ===
18:34:27.416/D: 正在初始化...
18:34:27.416/D: 当前使用Root Shell模式
18:34:27.416/D: 首次尝试获取root权限...
18:34:27.472/D: 成功获取root权限，标记为已授权
18:34:27.473/D: 正在进行设备注册...
18:34:27.473/D: 生成新的随机设备令牌
18:34:27.475/D: 新生成的设备令牌: device_1753871667474_8kq6f0dd
18:34:27.475/D: 发送设备注册请求: {"username":"admin","device_token":"device_1753871667474_8kq6f0dd","device_name":"xia<PERSON> Note 7","device_type":"mobile","is_temp":false}
18:34:27.476/D: 注册路径: http://xhss.ke999.cn/device/register
18:34:27.553/D: 注册响应状态码: 200
18:34:27.561/D: 设备注册结果: {"success":true,"message":"设备注册成功","data":{"device_name":"xiaomi Redmi Note 7","operation_count":0,"max_operations":3,"remaining_operations":3,"configs":[{"id":2,"user_id":1,"name":"操作几次恢复出厂设置","config":{"阅读延时":20000,"阅读开关":1,"跳转链接延时":3000,"操作几次恢复出厂设置":4},"description":"还原几次备份操作后恢复出厂设置","is_default":1,"created_at":"2025-07-04T10:28:17.000Z","updated_at":"2025-07-30T08:43:12.000Z"}]}}
18:34:27.569/D: 已保存默认配置: 阅读延时
18:34:27.576/D: 已设置全局变量: 阅读延时 = 20000
18:34:27.576/D: 已保存默认配置: 阅读开关
18:34:27.583/D: 已设置全局变量: 阅读开关 = 1
18:34:27.583/D: 已保存默认配置: 跳转链接延时
18:34:27.589/D: 已保存默认配置: 操作几次恢复出厂设置
18:34:27.596/D: 已设置全局变量: 操作几次恢复出厂设置 = 4
18:34:27.607/D: 从默认配置设置全局变量: 操作几次恢复出厂设置 = 4
18:34:27.608/D: 从默认配置设置全局变量: 阅读延时 = 20000
18:34:27.608/D: 从默认配置设置全局变量: 阅读开关 = 1
18:34:27.609/D: 设备注册成功，当前设备令牌: device_1753871667474_8kq6f0dd
18:34:27.609/D: 当前操作信息: {"操作次数":0,"最大操作次数":3,"剩余操作次数":3}
18:34:27.609/D: 设备注册成功，开始获取操作信息...
18:34:27.610/D: 当前操作信息: {"操作次数":0,"最大操作次数":3,"剩余操作次数":3}
18:34:27.610/D: 开始循环操作，从服务端获取最大操作次数: 3 次
18:34:27.611/D: 启动小红书应用...
18:34:27.611/D: 启动小红书应用
18:34:27.628/D: 小红书启动成功，等待应用加载...
18:34:27.629/D: 等待 50 毫秒
18:34:27.778/D: 小红书应用已成功启动并处于前台
18:34:27.779/D: [1/3] 获取链接中...
18:34:27.779/D: 发送GET请求到: http://xhss.ke999.cn/next-link
18:34:27.780/D: 请求数据: {"username":"admin","device_token":"device_1753871667474_8kq6f0dd"}
18:34:27.780/D: 完整GET请求URL: http://xhss.ke999.cn/next-link?username=admin&device_token=device_1753871667474_8kq6f0dd
18:34:27.888/D: 响应状态码: 200
18:34:27.893/D: 响应内容: {"success":true,"message":"获取链接成功","status":"success","data":{"link_id":191,"url":"http://xhslink.com/a/mlMsTydokzMfb","task_id":44,"original_likes":148,"current_likes":148,"target_likes":8,"actual_like_increase":0,"like_operations":3,"like_count":2,"original_collects":1555,"current_collects":1555,"target_collects":2,"actual_collect_increase":0,"collect_operations":3,"collect_count":2,"is_completed":false,"operation_count":0,"max_operations":3,"remaining_operations":3}}
18:34:27.896/D: 获取链接结果: {"success":true,"message":"获取链接成功","status":"success","data":{"link_id":191,"url":"http://xhslink.com/a/mlMsTydokzMfb","task_id":44,"original_likes":148,"current_likes":148,"target_likes":8,"actual_like_increase":0,"like_operations":3,"like_count":2,"original_collects":1555,"current_collects":1555,"target_collects":2,"actual_collect_increase":0,"collect_operations":3,"collect_count":2,"is_completed":false,"operation_count":0,"max_operations":3,"remaining_operations":3}}
18:34:27.904/D: 保存链接ID: 191
18:34:27.904/D: 获取到链接: http://xhslink.com/a/mlMsTydokzMfb
18:34:27.905/D: 链接ID: 191
18:34:27.905/D: 目标数量 - 点赞: 8, 收藏: 2
18:34:27.906/D: 原始数量 - 点赞: 148, 收藏: 1555
18:34:27.909/D: 任务类型: both, 目标点赞数: 8, 目标收藏数: 2
18:34:27.910/D: 实际需要: 点赞=true, 收藏=true
18:34:27.915/D: 获取到当前配置: 操作几次恢复出厂设置
18:34:27.916/D: 配置信息 - 浏览延时: 3秒, 操作间隔: 5秒, 阅读延时: 20000毫秒, 阅读开关: 1
18:34:27.922/D: 使用执行自动互动函数处理链接...
18:34:27.923/D: 执行自动互动操作: http://xhslink.com/a/mlMsTydokzMfb
18:34:27.923/D: 配置: 点赞=true, 收藏=true, 浏览延时=3秒
18:34:27.923/D: 目标数量: 点赞=8, 收藏=2
18:34:27.924/D: 原始数量: 点赞=148, 收藏=1555
18:34:27.924/D: 直接互动链接文章: http://xhslink.com/a/mlMsTydokzMfb
18:34:27.925/D: 操作选项: 点赞=true, 收藏=true, 浏览延时=3秒
18:34:27.925/D: 目标数量: 点赞=8, 收藏=2
18:34:27.925/D: 原始数量: 点赞=148, 收藏=1555
18:34:27.926/D: 阅读配置: 阅读延时=20000毫秒, 阅读开关=1
18:34:27.926/D: 等待 1000 毫秒
18:34:28.927/D: 已打开链接
18:34:28.927/D: 准备打开小红书链接: http://xhslink.com/a/mlMsTydokzMfb
18:34:28.927/D: URL类型: string
18:34:28.928/D: URL长度: 34
18:34:28.928/D: 检测到小红书短链接，开始解析...
18:34:28.929/D: 开始多策略解析短链接: http://xhslink.com/a/mlMsTydokzMfb
18:34:28.929/D: 原始URL: http://xhslink.com/a/mlMsTydokzMfb
18:34:28.929/D: URL字符编码检查: "http://xhslink.com/a/mlMsTydokzMfb"
18:34:28.929/D: 处理后的URL: http://xhslink.com/a/mlMsTydokzMfb
18:34:28.930/D: URL验证通过: http://xhslink.com/a/mlMsTydokzMfb
18:34:28.931/D: 尝试User-Agent 1/3
18:34:28.931/D: 请求URL: http://xhslink.com/a/mlMsTydokzMfb
18:34:28.932/D: URL类型: string
18:34:28.932/D: URL长度: 34
18:34:29.265/D: 响应状态码: 200
18:34:29.386/D: 尝试User-Agent 2/3
18:34:29.389/D: 请求URL: http://xhslink.com/a/mlMsTydokzMfb
18:34:29.390/D: URL类型: string
18:34:29.395/D: URL长度: 34
18:34:29.556/D: 响应状态码: 200
18:34:29.636/D: 尝试User-Agent 3/3
18:34:29.637/D: 请求URL: http://xhslink.com/a/mlMsTydokzMfb
18:34:29.639/D: URL类型: string
18:34:29.641/D: URL长度: 34
18:34:29.784/D: 响应状态码: 200
18:34:29.866/D: 尝试跟随重定向...
18:34:29.867/D: 跟随重定向URL: http://xhslink.com/a/mlMsTydokzMfb
18:34:30.028/D: 跟随重定向后的URL: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
18:34:30.030/D: 短链接解析成功: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
18:34:30.031/D: 准备打开小红书链接: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
18:34:30.034/D: URL类型: string
18:34:30.035/D: URL长度: 409
18:34:30.036/D: 检测到小红书长链接，提取ID
18:34:30.037/D: URL类型: string
18:34:30.038/D: URL内容: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
18:34:30.039/D: 提取笔记ID - 输入URL: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
18:34:30.041/D: 通过/discovery/item/路径提取到ID: 641c1b1a0000000027013c58
18:34:30.043/D: 从长链接提取到笔记ID: 641c1b1a0000000027013c58
18:34:30.045/D: 使用ID打开小红书: 641c1b1a0000000027013c58
18:34:30.062/D: 已发送打开请求
18:34:30.063/D: 等待 2000 毫秒
18:34:32.063/D: 开始处理打开流程，最大尝试次数: 50
18:34:32.064/D: 当前尝试次数: 1
18:34:32.064/D: 执行Root命令: dumpsys window | grep mCurrentFocus
18:34:32.150/D: 命令执行成功
18:34:32.151/D: 当前应用包名: com.xingin.xhs
18:34:32.151/D: 已检测到进入小红书应用
18:34:32.152/D: 等待 2000 毫秒
18:34:34.153/D: 获取页面信息，检查点赞状态和内容类型...
18:34:34.154/D: 开始获取界面 XML...
18:34:34.155/D: 使用ROOT方式shell命令获取XML
18:34:34.155/D: 开始获取界面 XML...
18:34:36.196/D: 成功获取 XML 文件，大小: 9813 字节
18:34:36.196/D: 界面 XML 导出成功
18:34:36.196/D: XML获取成功，开始解析页面元素...
18:34:36.197/D: 开始解析 XML 中的文本元素...
18:34:36.217/D: 共找到 11 个有效元素 (已过滤 20 个空文本元素)
18:34:36.223/D: 图文用户名: [一闪亮晶晶女装], 坐标: (391, 156)
18:34:36.224/D: 点赞数: [148], 坐标: (0, 0)
18:34:36.224/D: 收藏数: [1555], 坐标: (0, 0)
18:34:36.224/D: 评论数: [0], 坐标: (0, 0)
18:34:36.225/D: 内容类型: 图文
18:34:36.225/D: 未检测到分享按钮，使用左侧X偏移检测方式（图文界面）
18:34:36.225/D: 页面元素解析完成，后续操作将复用此数据，不再重复获取XML
18:34:36.226/D: 当前页面数量: 点赞=148, 收藏=1555
18:34:36.226/D: 开始判断点赞状态
18:34:36.226/D: 通过content-desc判断点赞状态: 已点赞
18:34:36.227/D: 当前点赞状态: 已点赞
18:34:36.227/D: 开始判断收藏状态
18:34:36.227/D: 通过content-desc判断收藏状态: 已收藏
18:34:36.227/D: 当前收藏状态: 已收藏
18:34:36.228/D: 点赞达标检查: 当前增长=0, 目标=8, 已达标=false (有初始值)
18:34:36.228/D: 收藏达标检查: 当前增长=0, 目标=2, 已达标=false (有初始值)
18:34:36.229/D: === 开始精准判断操作必要性 ===
18:34:36.230/D: ✓ 点赞任务：已完成（当前状态：已点赞）
18:34:36.230/D: ✓ 收藏任务：已完成（当前状态：已收藏）
18:34:36.230/D: === 判断结果：所有任务已完成，无需执行任何操作，直接返回成功 ===
18:34:36.230/D: 跳过浏览延时和互动操作，准备处理下一个链接
18:34:36.231/D: 操作结果: 已点赞，跳过, 已收藏，跳过
18:34:36.232/D: 提交到服务端的操作前数量: 点赞=148, 收藏=1555
18:34:36.232/D: 发送POST请求到: http://xhss.ke999.cn/update-status
18:34:36.233/D: 请求数据: {"username":"admin","device_token":"device_1753871667474_8kq6f0dd","link_id":191,"status":"success","operation_type":"both","before_like_count":148,"before_collect_count":1555}
18:34:36.292/D: 响应状态码: 200
18:34:36.300/D: 响应内容: {"success":true,"message":"操作成功","data":{"link_id":"191","original_likes":148,"current_likes":148,"target_likes":8,"actual_like_increase":0,"like_operations":4,"like_count":3,"original_collects":1555,"current_collects":1555,"target_collects":2,"actual_collect_increase":0,"collect_operations":4,"collect_count":3,"is_completed":false,"status":"active"}}
18:34:36.301/D: 更新状态结果: {"success":true,"message":"操作成功","data":{"link_id":"191","original_likes":148,"current_likes":148,"target_likes":8,"actual_like_increase":0,"like_operations":4,"like_count":3,"original_collects":1555,"current_collects":1555,"target_collects":2,"actual_collect_increase":0,"collect_operations":4,"collect_count":3,"is_completed":false,"status":"active"}}
18:34:36.302/D: 服务端返回操作信息 - 操作次数: 0, 最大操作次数: 0, 剩余操作次数: 0
18:34:36.309/D: 操作成功: 已点赞，跳过, 已收藏，跳过
18:34:36.311/D: 已点赞/已收藏，跳过操作间隔
18:34:36.814/D: 执行返回操作
18:34:36.816/D: 执行Root命令: input keyevent 4
18:34:37.451/D: 命令执行成功
18:34:37.452/D: 返回操作成功
18:34:37.452/D: [2/3] 获取链接中...
18:34:37.453/D: 发送GET请求到: http://xhss.ke999.cn/next-link
18:34:37.453/D: 请求数据: {"username":"admin","device_token":"device_1753871667474_8kq6f0dd"}
18:34:37.454/D: 完整GET请求URL: http://xhss.ke999.cn/next-link?username=admin&device_token=device_1753871667474_8kq6f0dd
18:34:37.516/D: 响应状态码: 200
18:34:37.519/D: 响应内容: {"success":true,"message":"获取链接成功","status":"success","data":{"link_id":190,"url":"http://xhslink.com/a/uGNS8M5tozMfb","task_id":44,"original_likes":132,"current_likes":132,"target_likes":8,"actual_like_increase":0,"like_operations":1,"like_count":1,"original_collects":1537,"current_collects":1537,"target_collects":2,"actual_collect_increase":0,"collect_operations":1,"collect_count":1,"is_completed":false,"operation_count":2,"max_operations":3,"remaining_operations":1}}
18:34:37.519/D: 获取链接结果: {"success":true,"message":"获取链接成功","status":"success","data":{"link_id":190,"url":"http://xhslink.com/a/uGNS8M5tozMfb","task_id":44,"original_likes":132,"current_likes":132,"target_likes":8,"actual_like_increase":0,"like_operations":1,"like_count":1,"original_collects":1537,"current_collects":1537,"target_collects":2,"actual_collect_increase":0,"collect_operations":1,"collect_count":1,"is_completed":false,"operation_count":2,"max_operations":3,"remaining_operations":1}}
18:34:37.528/D: 保存链接ID: 190
18:34:37.529/D: 获取到链接: http://xhslink.com/a/uGNS8M5tozMfb
18:34:37.529/D: 链接ID: 190
18:34:37.529/D: 目标数量 - 点赞: 8, 收藏: 2
18:34:37.530/D: 原始数量 - 点赞: 132, 收藏: 1537
18:34:37.533/D: 任务类型: both, 目标点赞数: 8, 目标收藏数: 2
18:34:37.534/D: 实际需要: 点赞=true, 收藏=true
18:34:37.536/D: 获取到当前配置: 操作几次恢复出厂设置
18:34:37.536/D: 配置信息 - 浏览延时: 3秒, 操作间隔: 5秒, 阅读延时: 20000毫秒, 阅读开关: 1
18:34:37.537/D: 使用执行自动互动函数处理链接...
18:34:37.537/D: 执行自动互动操作: http://xhslink.com/a/uGNS8M5tozMfb
18:34:37.537/D: 配置: 点赞=true, 收藏=true, 浏览延时=3秒
18:34:37.537/D: 目标数量: 点赞=8, 收藏=2
18:34:37.537/D: 原始数量: 点赞=132, 收藏=1537
18:34:37.538/D: 直接互动链接文章: http://xhslink.com/a/uGNS8M5tozMfb
18:34:37.538/D: 操作选项: 点赞=true, 收藏=true, 浏览延时=3秒
18:34:37.538/D: 目标数量: 点赞=8, 收藏=2
18:34:37.538/D: 原始数量: 点赞=132, 收藏=1537
18:34:37.538/D: 阅读配置: 阅读延时=20000毫秒, 阅读开关=1
18:34:37.539/D: 等待 1000 毫秒
18:34:38.540/D: 已打开链接
18:34:38.542/D: 准备打开小红书链接: http://xhslink.com/a/uGNS8M5tozMfb
18:34:38.543/D: URL类型: string
18:34:38.544/D: URL长度: 34
18:34:38.545/D: 检测到小红书短链接，开始解析...
18:34:38.546/D: 开始多策略解析短链接: http://xhslink.com/a/uGNS8M5tozMfb
18:34:38.547/D: 原始URL: http://xhslink.com/a/uGNS8M5tozMfb
18:34:38.549/D: URL字符编码检查: "http://xhslink.com/a/uGNS8M5tozMfb"
18:34:38.551/D: 处理后的URL: http://xhslink.com/a/uGNS8M5tozMfb
18:34:38.553/D: URL验证通过: http://xhslink.com/a/uGNS8M5tozMfb
18:34:38.554/D: 尝试User-Agent 1/3
18:34:38.554/D: 请求URL: http://xhslink.com/a/uGNS8M5tozMfb
18:34:38.554/D: URL类型: string
18:34:38.555/D: URL长度: 34
18:34:38.855/D: 响应状态码: 200
18:34:38.989/D: 尝试User-Agent 2/3
18:34:38.991/D: 请求URL: http://xhslink.com/a/uGNS8M5tozMfb
18:34:38.995/D: URL类型: string
18:34:38.997/D: URL长度: 34
18:34:39.202/D: 响应状态码: 200
18:34:39.286/D: 尝试User-Agent 3/3
18:34:39.289/D: 请求URL: http://xhslink.com/a/uGNS8M5tozMfb
18:34:39.291/D: URL类型: string
18:34:39.293/D: URL长度: 34
18:34:39.454/D: 响应状态码: 200
18:34:39.546/D: 尝试跟随重定向...
18:34:39.549/D: 跟随重定向URL: http://xhslink.com/a/uGNS8M5tozMfb
18:34:39.738/D: 跟随重定向后的URL: https://www.xiaohongshu.com/discovery/item/641c1aba0000000013008958?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkfw9_0rHgF3UwVa4YQY68W5o%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775921&share_id=161e35ffc335467197ac33a98e71e166&share_channel=copy_link
18:34:39.745/D: 短链接解析成功: https://www.xiaohongshu.com/discovery/item/641c1aba0000000013008958?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkfw9_0rHgF3UwVa4YQY68W5o%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775921&share_id=161e35ffc335467197ac33a98e71e166&share_channel=copy_link
18:34:39.747/D: 准备打开小红书链接: https://www.xiaohongshu.com/discovery/item/641c1aba0000000013008958?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkfw9_0rHgF3UwVa4YQY68W5o%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775921&share_id=161e35ffc335467197ac33a98e71e166&share_channel=copy_link
18:34:39.749/D: URL类型: string
18:34:39.753/D: URL长度: 409
18:34:39.756/D: 检测到小红书长链接，提取ID
18:34:39.759/D: URL类型: string
18:34:39.761/D: URL内容: https://www.xiaohongshu.com/discovery/item/641c1aba0000000013008958?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkfw9_0rHgF3UwVa4YQY68W5o%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775921&share_id=161e35ffc335467197ac33a98e71e166&share_channel=copy_link
18:34:39.766/D: 提取笔记ID - 输入URL: https://www.xiaohongshu.com/discovery/item/641c1aba0000000013008958?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkfw9_0rHgF3UwVa4YQY68W5o%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775921&share_id=161e35ffc335467197ac33a98e71e166&share_channel=copy_link
18:34:39.768/D: 通过/discovery/item/路径提取到ID: 641c1aba0000000013008958
18:34:39.769/D: 从长链接提取到笔记ID: 641c1aba0000000013008958
18:34:39.770/D: 使用ID打开小红书: 641c1aba0000000013008958
18:34:39.791/D: 已发送打开请求
18:34:39.792/D: 等待 2000 毫秒
18:34:41.517/V: [$remote/xhs root直接跳转服务端版.js] 运行结束 (用时 14.205 秒)

18:34:41.518/D: 脚本即将退出，执行清理工作...
18:34:41.518/D: 清理工作完成
