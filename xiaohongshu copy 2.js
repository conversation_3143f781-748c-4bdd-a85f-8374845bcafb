﻿/**
 * 小红书API操作模块
 * 包含API接口调用和互动元素获取功能
 * 
 * 功能列表：
 * 1. 设备注册API
 * 2. 获取链接API
 * 3. 更新操作状态API
 * 4. 设备令牌管理
 * 5. 操作信息管理
 * 6. 互动元素获取
 * 
 * 作者: Claude
 * 日期: 2024-07-12
 */
// 获取屏幕尺寸 - 兼容Node.js环境
let 屏幕宽度 = typeof device !== 'undefined' ? device.width : 1080;
let 屏幕高度 = typeof device !== 'undefined' ? device.height : 2340;
// 在文件开头添加一个标志，表示是否是首次运行
let 是否首次运行 = true;
// 引入DeviceOperation模块，用于设备操作 - 兼容打包环境
var DeviceOperation;
try {
    DeviceOperation = require('./DeviceOperation.js');
} catch (e) {
    try {
        DeviceOperation = require('DeviceOperation.js');
    } catch (e2) {
        console.error("xiaohongshu.js: DeviceOperation模块加载失败:", e.message);
        throw e;
    }
}
/**
 * API配置
 * 根据API调用说明文档配置API路径
 */
const API配置 = {
    基本URL: "http://xhss.ke999.cn",  // 移除"/api"前缀
    用户名: "admin", // 请在此处填写您的用户名
    设备令牌: "", // 如果为空，将自动生成
    设备名称: typeof device !== 'undefined' ? (device.brand + " " + device.model) : "未知设备" // 自动获取设备名称
};

// 检查AutoJS环境
if (typeof auto === 'undefined') {
    console.log("警告：未在AutoJS环境中运行，部分功能可能不可用");
    // 创建模拟device对象，避免报错
    if (typeof device === 'undefined') {
        global.device = {
            width: 1080,
            height: 1920,
            brand: "模拟",
            model: "设备",
            release: "未知"
        };
    }
}

console.log("小红书API模块已加载");

/**
 * 生成随机设备令牌
 * 
 * @returns {string} - 随机生成的设备令牌
 */
function 生成设备令牌() {
    // 使用与网页测试一致的格式：device_时间戳_随机字符串
    const 时间戳 = Date.now();
    let 随机字符串 = "";
    const 可能字符 = "abcdefghijklmnopqrstuvwxyz0123456789";

    // 生成8位随机字符串
    for (let i = 0; i < 8; i++) {
        随机字符串 += 可能字符.charAt(Math.floor(Math.random() * 可能字符.length));
    }

    // 格式: device_时间戳_随机字符串
    return `device_${时间戳}_${随机字符串}`;
}

/**
 * 获取或初始化设备令牌
 * 每次启动都生成新的随机设备令牌
 *
 * @returns {string} - 设备令牌
 */
function 获取设备令牌() {
    // 如果已经设置了设备令牌，直接返回
    if (API配置.设备令牌) {
        return API配置.设备令牌;
    }

    // 每次启动都生成新的随机设备令牌，不再使用存储的固定令牌
    console.log("生成新的随机设备令牌");
    let 新令牌 = 生成设备令牌();
    API配置.设备令牌 = 新令牌;

    console.log("新生成的设备令牌: " + 新令牌);
    return 新令牌;
}

/**
 * 更新设备令牌（当达到每日限制时调用）
 * 
 * @returns {string} - 新的设备令牌
 */
function 更新设备令牌() {
    let 新令牌 = 生成设备令牌();
    API配置.设备令牌 = 新令牌;

    // 保存到存储
    storages.create("小红书操作").put("设备令牌", 新令牌);

    console.log("已更新设备令牌: " + 新令牌);
    return 新令牌;
}

/**
 * 发送HTTP请求
 * 
 * @param {string} 方法 - 请求方法，"GET"或"POST"
 * @param {string} 路径 - API路径
 * @param {Object} 数据 - 请求数据
 * @returns {Object} - 响应数据
 */
function 发送请求(方法, 路径, 数据) {
    let url = API配置.基本URL + 路径;

    console.log(`发送${方法}请求到: ${url}`);
    console.log(`请求数据: ${JSON.stringify(数据)}`);

    try {
        let response;

        if (方法 === "GET" && 数据) {
            // 构建查询字符串
            let 查询参数 = [];
            for (let key in 数据) {
                查询参数.push(encodeURIComponent(key) + "=" + encodeURIComponent(数据[key]));
            }
            url += "?" + 查询参数.join("&");
            console.log(`完整GET请求URL: ${url}`);
            response = http.get(url);
        } else if (方法 === "POST") {
            // 发送POST请求，不设置contentType
            response = http.post(url, 数据);
        } else {
            response = http.get(url);
        }

        // 记录响应状态码
        console.log(`响应状态码: ${response.statusCode}`);

        if (response.statusCode >= 200 && response.statusCode < 300) {
            let 响应内容 = response.body.json();
            console.log(`响应内容: ${JSON.stringify(响应内容)}`);
            return 响应内容;
        } else {
            // 详细记录错误信息
            let 错误信息 = `请求失败，状态码: ${response.statusCode}`;
            try {
                let 错误响应 = response.body.string();
                console.error(错误信息);
                console.error(`错误响应: ${错误响应}`);
                return {
                    success: false,
                    message: 错误信息,
                    error_details: 错误响应
                };
            } catch (e) {
                console.error(`${错误信息}, 无法解析响应内容`);
                return { success: false, message: 错误信息 };
            }
        }
    } catch (e) {
        console.error(`发送请求出错: ${e.message}`);
        console.error(`错误堆栈: ${e.stack}`);
        return {
            success: false,
            message: `发送请求出错: ${e.message}`,
            error_stack: e.stack
        };
    }
}

/**
 * 设备注册API
 * 
 * @param {string} 设备类型 - 可选，设备类型，默认为'mobile'
 * @param {boolean} 是否临时设备 - 可选，是否为临时设备，默认为false
 * @returns {Object} - 注册结果
 */
function 设备注册(设备类型 = 'mobile', 是否临时设备 = false) {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;

    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }

    let 数据 = {
        username: 用户名,
        device_token: 设备令牌,
        device_name: API配置.设备名称,
        device_type: 设备类型,
        is_temp: 是否临时设备
    };

    console.log("发送设备注册请求: " + JSON.stringify(数据));

    try {
        // 构建URL - 使用与网页测试一致的路径
        let url = API配置.基本URL + "/device/register";
        console.log(`注册路径: ${url}`);

        // 使用POST请求，不设置contentType
        let 响应 = http.post(url, 数据);

        console.log(`注册响应状态码: ${响应.statusCode}`);

        if (响应.statusCode >= 200 && 响应.statusCode < 300) {
            let 结果 = 响应.body.json();
            console.log("设备注册结果: " + JSON.stringify(结果));

            // 保存操作次数信息和配置信息
            if (结果.success && 结果.data) {
                // 保存操作信息
                let 操作信息 = {
                    操作次数: 结果.data.operation_count || 0,
                    最大操作次数: 结果.data.max_operations || 0,
                    剩余操作次数: 结果.data.remaining_operations || 0
                };
                storages.create("小红书操作").put("操作信息", 操作信息);

                // 检查是否已达到每日操作上限
                if (操作信息.剩余操作次数 <= 0) {
                    console.log("当前设备今日操作次数已达上限，无法继续操作");
                    toast("今日操作次数已达上限，即将退出本次注册");
                    sleep(3000);
                    return { success: false, message: "今日操作次数已达上限", limitReached: true };
                }

                // 保存设备ID
                if (结果.data.device_id) {
                    storages.create("小红书操作").put("设备ID", 结果.data.device_id);
                }

                // 保存配置信息
                if (结果.data.configs && Array.isArray(结果.data.configs) && 结果.data.configs.length > 0) {
                    // 提取所有配置项
                    let 所有配置 = {};

                    // 遍历所有配置
                    for (let i = 0; i < 结果.data.configs.length; i++) {
                        let 配置项 = 结果.data.configs[i];

                        // 如果配置有name和config属性
                        if (配置项.name && 配置项.config) {
                            // 将配置添加到所有配置对象
                            所有配置[配置项.name] = 配置项.config;

                            // 如果是默认配置，单独保存
                            if (配置项.is_default === 1) {
                                for (let 键 in 配置项.config) {
                                    console.log(`已保存默认配置: ${键}`);
                                    storages.create("小红书操作").put(键, 配置项.config[键]);

                                    // 特别处理"操作几次恢复出厂设置"，设置为全局变量
                                    if (键 === "操作几次恢复出厂设置") {
                                        // 设置为全局变量，方便其他模块访问
                                        global.操作几次恢复出厂设置 = 配置项.config[键];
                                        console.log(`已设置全局变量: 操作几次恢复出厂设置 = ${global.操作几次恢复出厂设置}`);
                                    }

                                    // 特别处理"阅读延时"，设置为全局变量
                                    if (键 === "阅读延时") {
                                        global.阅读延时 = 配置项.config[键];
                                        console.log(`已设置全局变量: 阅读延时 = ${global.阅读延时}`);
                                    }

                                    // 特别处理"阅读开关"，设置为全局变量
                                    if (键 === "阅读开关") {
                                        global.阅读开关 = 配置项.config[键];
                                        console.log(`已设置全局变量: 阅读开关 = ${global.阅读开关}`);
                                    }
                                }
                            }
                        }
                    }

                    // 保存所有配置
                    storages.create("小红书操作").put("所有配置", 所有配置);

                    // 寻找默认配置
                    let 默认配置 = 结果.data.configs.find(配置 => 配置.is_default === 1);

                    // 如果没有默认配置，使用第一个配置
                    if (!默认配置 && 结果.data.configs.length > 0) {
                        默认配置 = 结果.data.configs[0];
                    }

                    // 保存默认配置
                    if (默认配置) {
                        storages.create("小红书操作").put("当前配置", 默认配置);

                        // 如果默认配置中有"操作几次恢复出厂设置"，设置为全局变量
                        if (默认配置.config && 默认配置.config.操作几次恢复出厂设置 !== undefined) {
                            global.操作几次恢复出厂设置 = 默认配置.config.操作几次恢复出厂设置;
                            console.log(`从默认配置设置全局变量: 操作几次恢复出厂设置 = ${global.操作几次恢复出厂设置}`);
                        }

                        // 如果默认配置中有"阅读延时"，设置为全局变量
                        if (默认配置.config && 默认配置.config.阅读延时 !== undefined) {
                            global.阅读延时 = 默认配置.config.阅读延时;
                            console.log(`从默认配置设置全局变量: 阅读延时 = ${global.阅读延时}`);
                        }

                        // 如果默认配置中有"阅读开关"，设置为全局变量
                        if (默认配置.config && 默认配置.config.阅读开关 !== undefined) {
                            global.阅读开关 = 默认配置.config.阅读开关;
                            console.log(`从默认配置设置全局变量: 阅读开关 = ${global.阅读开关}`);
                        }
                    }
                }

                // 输出设备令牌，便于调试
                console.log("设备注册成功，当前设备令牌: " + 设备令牌);
                console.log("当前操作信息: " + JSON.stringify(操作信息));
            }

            return 结果;
        } else {
            // 请求失败
            let 错误信息 = `注册请求失败，状态码: ${响应.statusCode}`;
            try {
                let 错误响应 = 响应.body.string();
                console.error(错误信息);
                console.error(`错误响应: ${错误响应}`);
                return {
                    success: false,
                    message: 错误信息,
                    error_details: 错误响应
                };
            } catch (e) {
                console.error(`${错误信息}, 无法解析响应内容`);
                return { success: false, message: 错误信息 };
            }
        }
    } catch (e) {
        console.error(`发送注册请求出错: ${e.message}`);
        console.error(`错误堆栈: ${e.stack}`);
        return {
            success: false,
            message: `发送注册请求出错: ${e.message}`,
            error_stack: e.stack
        };
    }
}

/**
 * 获取下一个链接API
 * 
 * @returns {Object} - 链接数据
 */
function 获取下一个链接() {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;

    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }

    let 数据 = {
        username: 用户名,  // 调整参数顺序，与网页测试一致
        device_token: 设备令牌
    };

    let 结果 = 发送请求("GET", "/next-link", 数据);
    console.log("获取链接结果: " + JSON.stringify(结果));

    // 显示toast提示消息
    if (结果.toast && 结果.toast.message) {
        toast(结果.toast.message);
    } else if (结果.status === "no_links") {
        toast("当前没有可操作的链接，30秒后将重试");
    } else if (结果.status === "limit_reached") {
        console.log("服务端提示已达到每日操作上限");
        // 不再更新设备令牌，因为limit_reached表示当前设备已达上限
        // 更新设备令牌应该由调用方根据业务逻辑决定，而不是在获取链接时自动更新
    } else if (结果.success && 结果.status === "success") {
        toast("获取链接成功，正在处理...");
    }

    // 移除自动更新设备令牌的逻辑
    // 原因：limit_reached表示当前设备已达上限，不应该自动更新设备令牌
    // 如果需要更新设备令牌，应该由调用方根据具体业务场景决定

    // 更新操作次数信息
    if (结果.success && 结果.data) {
        let 操作信息 = {
            操作次数: 结果.data.operation_count || 0,
            最大操作次数: 结果.data.max_operations || 0,
            剩余操作次数: (结果.data.max_operations || 0) - (结果.data.operation_count || 0)
        };
        storages.create("小红书操作").put("操作信息", 操作信息);

        // 保存当前链接ID，供更新状态使用
        if (结果.data.link_id) {
            storages.create("小红书操作").put("当前链接ID", 结果.data.link_id);
            console.log("保存链接ID: " + 结果.data.link_id);
        }
    }

    return 结果;
}

/**
 * 更新操作状态API
 * 
 * @param {number} 链接ID - 链接ID
 * @param {string} 状态 - 操作状态: success(成功)或failed(失败)
 * @param {string} 操作类型 - 操作类型: like(点赞)、collect(收藏)或both(两者)
 * @param {number} 操作前点赞数 - 操作前的点赞数量
 * @param {number} 操作前收藏数 - 操作前的收藏数量
 * @param {string} 错误信息 - 错误信息(仅当status=failed时需要)
 * @returns {Object} - 更新结果
 */
function 更新操作状态(链接ID, 状态, 操作类型, 操作前点赞数, 操作前收藏数, 错误信息) {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;

    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }

    let 数据 = {
        username: 用户名,  // 调整参数顺序，与网页测试一致
        device_token: 设备令牌,
        link_id: 链接ID,
        status: 状态,
        operation_type: 操作类型,
        before_like_count: 操作前点赞数,
        before_collect_count: 操作前收藏数
    };

    if (状态 === "failed" && 错误信息) {
        数据.error_message = 错误信息;
    }

    let 结果 = 发送请求("POST", "/update-status", 数据);
    console.log("更新状态结果: " + JSON.stringify(结果));

    // 更新操作次数信息
    if (结果.success && 结果.data) {
        let 操作信息 = {
            操作次数: 结果.data.operation_count || 0,
            最大操作次数: 结果.data.max_operations || 0,
            剩余操作次数: 结果.data.remaining_operations || 0
        };

        // 记录服务端返回的操作次数信息，用于调试
        console.log(`服务端返回操作信息 - 操作次数: ${操作信息.操作次数}, 最大操作次数: ${操作信息.最大操作次数}, 剩余操作次数: ${操作信息.剩余操作次数}`);

        storages.create("小红书操作").put("操作信息", 操作信息);
    }

    return 结果;
}

/**
 * 获取当前操作信息
 * 
 * @returns {Object} - 操作信息
 */
function 获取操作信息() {
    let 存储的信息 = storages.create("小红书操作").get("操作信息");
    if (!存储的信息) {
        return {
            操作次数: 0,
            最大操作次数: 0,
            剩余操作次数: 0
        };
    }

    // 确保剩余操作次数不是NaN
    if (isNaN(存储的信息.剩余操作次数)) {
        存储的信息.剩余操作次数 = 存储的信息.最大操作次数 - 存储的信息.操作次数;
        if (存储的信息.剩余操作次数 < 0) 存储的信息.剩余操作次数 = 0;
        storages.create("小红书操作").put("操作信息", 存储的信息);
    }

    return 存储的信息;
}

/**
 * 获取当前配置
 * 
 * @returns {Object|null} - 当前配置信息，如果没有则返回null
 */
function 获取当前配置() {
    let 当前配置 = storages.create("小红书操作").get("当前配置");
    if (!当前配置) {
        console.log("未找到当前配置");
        return null;
    }

    console.log("获取到当前配置: " + 当前配置.name);
    return 当前配置;
}

/**
 * 设置API配置
 * 
 * @param {string} 基本URL - API基本URL
 * @param {string} 用户名 - 用户名
 * @param {string} 设备令牌 - 设备令牌
 * @param {string} 设备名称 - 设备名称
 */
function 设置API配置(基本URL, 用户名, 设备令牌, 设备名称) {
    if (基本URL) API配置.基本URL = 基本URL;
    if (用户名) API配置.用户名 = 用户名;
    if (设备令牌) API配置.设备令牌 = 设备令牌;
    if (设备名称) API配置.设备名称 = 设备名称;
    console.log("API配置已更新: " + JSON.stringify(API配置));
}


/**
 * 从文本中提取数字
 * 优化版本：提高提取效率，减少日志输出
 * 
 * @param {string} text - 包含数字的文本
 * @returns {number|null} - 提取的数字，失败返回null
 */
function 提取数字(text) {
    if (!text) return null;

    try {
        // 先移除所有空格，确保能处理"点赞 1445"这种格式
        let cleanText = text.replace(/\s+/g, "");

        // 匹配数字部分（包括小数和千分位）
        let 数字匹配 = cleanText.match(/\d+(\.\d+)?/);
        if (数字匹配) {
            // 转换为数字
            let 提取结果 = parseFloat(数字匹配[0]);
            return 提取结果;
        }

        // 如果是纯数字文本，直接解析
        if (/^\d+$/.test(text)) {
            let 提取结果 = parseInt(text);
            return 提取结果;
        }

        return null;
    } catch (e) {
        return null;
    }
}















// 全局变量，用于标记是否已经在运行
let 正在运行 = false;

// 全局变量，操作模式: 1=无障碍模式, 2=Root Shell模式
let 操作模式 = 2; // 默认使用Root Shell模式

// 全局变量，保存root会话状态
let root会话 = false;

// 全局变量，按钮颜色配置
let 颜色配置 = {
    点赞颜色: "FF2442",  // 红色系
    收藏颜色: "FCBD54",  // 黄色系
    颜色容差: 40         // 颜色匹配的容差值
};

/**
 * 判断颜色是否匹配
 * @param {number} r - 当前像素的红色分量
 * @param {number} g - 当前像素的绿色分量
 * @param {number} b - 当前像素的蓝色分量
 * @param {string} colorCode - 目标颜色代码，格式为RRGGBB
 * @param {number} tolerance - 容差值，默认为30
 * @returns {boolean} 是否匹配
 */
function 颜色匹配(r, g, b, colorCode, tolerance = 30) {
    // 解析颜色代码
    let targetR = parseInt(colorCode.substring(0, 2), 16);
    let targetG = parseInt(colorCode.substring(2, 4), 16);
    let targetB = parseInt(colorCode.substring(4, 6), 16);

    // 计算颜色差异
    let diffR = Math.abs(r - targetR);
    let diffG = Math.abs(g - targetG);
    let diffB = Math.abs(b - targetB);

    // 判断是否在容差范围内
    return diffR <= tolerance && diffG <= tolerance && diffB <= tolerance;
}

/**
 * 颜色调试函数，将RGB转为十六进制颜色代码
 * @param {number} r - 红色分量
 * @param {number} g - 绿色分量
 * @param {number} b - 蓝色分量
 * @returns {string} 十六进制颜色代码
 */
function RGB转十六进制(r, g, b) {
    function 转两位十六进制(n) {
        let hex = n.toString(16).toUpperCase();
        return hex.length === 1 ? '0' + hex : hex;
    }
    return 转两位十六进制(r) + 转两位十六进制(g) + 转两位十六进制(b);
}

// UI功能已移除，改为服务端版本

// UI设置功能已移除，改为服务端版本

/**
 * 服务端版本主执行函数
 * @param {Object} 配置 - 执行配置参数
 */
function 服务端执行主功能(配置 = {}) {
    // 防止重复运行
    if (正在运行) {
        console.log("任务已在运行中，请等待完成");
        return false;
    }

    正在运行 = true;

    // 设置默认配置
    let 默认配置 = {
        操作间隔: 5,
        浏览延时: 3,
        需要点赞: true,
        需要收藏: false
    };

    // 合并配置
    let 最终配置 = Object.assign(默认配置, 配置);

    console.log(`服务端执行配置: ${JSON.stringify(最终配置)}`);

    // 检查是否至少选择了一个互动选项
    if (!最终配置.需要点赞 && !最终配置.需要收藏) {
        console.log("请至少选择一个互动选项（点赞或收藏）");
        正在运行 = false;
        return false;
    }

    try {
        // 根据操作模式检查权限
        if (操作模式 === 1) {
            // 无障碍模式，检查无障碍服务是否已启用
            if (!auto.service) {
                console.log("无障碍服务未启用，尝试启动...");
                auto.waitFor();
            }
        } else if (操作模式 === 2) {
            // Root模式，检查Root权限
            if (!初始化Root权限()) {
                console.log("警告：未获取到 root 权限，脚本可能无法正常工作");
                正在运行 = false;
                return false;
            }
            console.log("Root权限检查通过");
        }

        // 请求截屏权限
        if (!requestScreenCapture()) {
            console.log("请求截屏权限失败");
            正在运行 = false;
            return false;
        }

        // 执行主要功能 - 调用小红书点赞操作入口
        return 小红书点赞操作入口();
    } catch (e) {
        console.error("执行过程中出错: " + e);
        return false;
    } finally {
        // 无论如何都要重置运行状态
        正在运行 = false;
    }
}

// 添加脚本退出时的清理函数 - 兼容Node.js环境
if (typeof events !== 'undefined') {
    events.on("exit", function () {
    console.log("脚本即将退出，执行清理工作...");

    // 重置root会话状态
    root会话 = false;

    console.log("清理工作完成");
    });
}

/**
 * 初始化Root权限，获取一个root会话
 * @returns {boolean} 是否成功获取root权限
 */
function 初始化Root权限() {
    // 如果已经获取过root权限，直接返回true，不再重复申请
    if (root会话) {
        console.log("已有root会话，无需重新申请权限");
        return true;
    }

    console.log("首次尝试获取root权限...");

    try {
        // 使用普通shell命令测试root权限
        let result = shell("su -c 'echo root_test'", true);

        if (result.code === 0 && result.result.includes("root_test")) {
            console.log("成功获取root权限，标记为已授权");
            root会话 = true;
            return true;
        } else {
            console.log("获取root权限失败: " + result.error);
            root会话 = false;
            return false;
        }
    } catch (e) {
        console.error("初始化Root权限出错: " + e);
        root会话 = false;
        return false;
    }
}

//---------
//=====================

// 检查操作模式函数
function 检查操作模式() {
    if (操作模式 === 1) {
        console.log("当前使用无障碍模式操作，请确保已开启无障碍服务");
        // 检查无障碍服务是否已启用
        if (!auto.service) {
            console.log("无障碍服务未启用，尝试启动...");
            auto.waitFor();
        }
    } else if (操作模式 === 2) {
        console.log("当前使用Root Shell模式操作，请确保已获取Root权限");
        // 检查Root权限，如果没有root会话，则尝试初始化一次
        if (!root会话) {
            if (!初始化Root权限()) {
                console.log("警告：未获取到 root 权限，脚本可能无法正常工作");
                return false;
            }
        }
        console.log("Root权限检查通过");
        return true;
    } else {
        console.log("错误：未知的操作模式，请设置为1(无障碍)或2(Root Shell)");
        return false;
    }
    return true;
}

/**
 * 使用 uiautomator dump 获取当前界面的 XML 结构，增加内部重试机制
 * @returns {string|null} XML 内容或 null（如果失败）
 */
function 获取界面XML() {
    console.log("开始获取界面 XML...");

    // 最大内部重试次数
    const 最大重试次数 = 3;
    let 当前重试次数 = 0;

    while (当前重试次数 < 最大重试次数) {
        let pageXml = 通过shell获取窗口XML();
        if (pageXml) {
            console.log("界面 XML 导出成功");
            try {
                return pageXml;
            } catch (e) {
                console.error("读取 XML 文件失败: " + e.message);
                当前重试次数++;

                if (当前重试次数 < 最大重试次数) {
                    console.log(`读取XML文件失败，进行第${当前重试次数 + 1}次重试...`);
                    等待(1000); // 等待1秒后重试
                }
            }
        } else {
            console.error("界面 XML 导出失败: " + result.error);
            当前重试次数++;

            if (当前重试次数 < 最大重试次数) {
                console.log(`导出XML失败，进行第${当前重试次数 + 1}次重试...`);
                等待(1500); // 失败后等待更长时间再重试
            }
        }
    }

    console.error(`获取界面XML失败，已重试${最大重试次数}次`);
    return null;
}


function 通过shell获取窗口XML() {
    console.log("开始获取界面 XML...");
    for (let i = 0; i < 10; i++) {
        try {
            // 使用su -c命令直接执行，这种方式在MIUI系统上更可靠
            let cmd = `su -c "uiautomator dump --compressed /sdcard/window_dump.xml"`;
            let result = shell(cmd, true);  // 执行命令，true 表示使用 Root 权限
            // 读取 XML 文件内容
            let xmlPath = "/sdcard/window_dump.xml";
            let xmlContent = files.read(xmlPath);
            //console.log(xmlContent);
            if (xmlContent && xmlContent.length > 100) {
                console.log("成功获取 XML 文件，大小: " + xmlContent.length + " 字节");
                return xmlContent;
            } else {
                console.error("XML内容为空或过短");
                //return null;
                sleep(1000);
            }
        } catch (e) {
            console.error("获取界面XML出错: " + e.message);
            //return null;
            sleep(1000);
        }
    }
    return null;
}


/**
 * 从 XML 中提取所有文本元素及其坐标，增强版支持content-desc属性
 * @param {string} xmlContent - XML 内容
 * @returns {Array} 元素数组，每个元素包含文本和坐标
 */
function 提取文本元素(xmlContent) {
    console.log("开始解析 XML 中的文本元素...");

    let 元素列表 = [];
    let 空文本计数 = 0;

    // 1. 提取text属性的元素
    let 文本正则 = /text="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/g;
    let 匹配结果;

    while ((匹配结果 = 文本正则.exec(xmlContent)) !== null) {
        let 文本 = 匹配结果[1];

        // 跳过空文本内容
        if (!文本 || 文本.trim() === "") {
            空文本计数++;
            continue;
        }

        let 左 = parseInt(匹配结果[2]);
        let 上 = parseInt(匹配结果[3]);
        let 右 = parseInt(匹配结果[4]);
        let 下 = parseInt(匹配结果[5]);

        // 计算中心点坐标
        let 中心X = Math.floor((左 + 右) / 2);
        let 中心Y = Math.floor((上 + 下) / 2);

        元素列表.push({
            文本: 文本,
            类型: "text",
            坐标: {
                左: 左,
                上: 上,
                右: 右,
                下: 下,
                中心X: 中心X,
                中心Y: 中心Y
            }
        });
    }

    // 2. 提取content-desc属性的元素（点赞、收藏、评论按钮）
    let 描述正则 = /content-desc="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/g;

    while ((匹配结果 = 描述正则.exec(xmlContent)) !== null) {
        let 描述 = 匹配结果[1];

        // 只处理包含点赞、收藏、评论的描述
        if (!描述 || (!描述.includes("点赞") && !描述.includes("收藏") && !描述.includes("评论"))) {
            continue;
        }

        let 左 = parseInt(匹配结果[2]);
        let 上 = parseInt(匹配结果[3]);
        let 右 = parseInt(匹配结果[4]);
        let 下 = parseInt(匹配结果[5]);

        // 计算中心点坐标
        let 中心X = Math.floor((左 + 右) / 2);
        let 中心Y = Math.floor((上 + 下) / 2);

        // 解析描述中的数字和状态
        let 解析结果 = 解析互动描述(描述);

        元素列表.push({
            文本: 解析结果.数字,
            类型: "content-desc",
            原始描述: 描述,
            互动类型: 解析结果.类型,
            已操作: 解析结果.已操作,
            坐标: {
                左: 左,
                上: 上,
                右: 右,
                下: 下,
                中心X: 中心X,
                中心Y: 中心Y
            }
        });
    }

    console.log("共找到 " + 元素列表.length + " 个有效元素 (已过滤 " + 空文本计数 + " 个空文本元素)");
    return 元素列表;
}

/**
 * 解析互动描述，提取数字和状态
 * @param {string} 描述 - content-desc的值，如"点赞 33"、"已点赞1130"、"收藏137"
 * @returns {Object} 包含类型、数字、已操作状态的对象
 */
function 解析互动描述(描述) {
    let 结果 = {
        类型: "",
        数字: "0",
        已操作: false
    };

    if (描述.includes("点赞")) {
        结果.类型 = "点赞";
        结果.已操作 = 描述.includes("已点赞");

        // 提取数字：支持"点赞 33"、"已点赞1130"等格式
        let 数字匹配 = 描述.match(/(\d+)/);
        if (数字匹配) {
            结果.数字 = 数字匹配[1];
        }
    } else if (描述.includes("收藏")) {
        结果.类型 = "收藏";
        结果.已操作 = 描述.includes("已收藏");

        // 提取数字：支持"收藏 37"、"已收藏1091"等格式
        let 数字匹配 = 描述.match(/(\d+)/);
        if (数字匹配) {
            结果.数字 = 数字匹配[1];
        }
    } else if (描述.includes("评论")) {
        结果.类型 = "评论";
        结果.已操作 = false; // 评论通常没有"已评论"状态

        // 提取数字：支持"评论 0"、"评论60"等格式
        let 数字匹配 = 描述.match(/(\d+)/);
        if (数字匹配) {
            结果.数字 = 数字匹配[1];
        }
    }

    return 结果;
}

/**
 * 从 XML 中提取特定文本的元素
 * @param {string} xmlContent - XML 内容
 * @param {string} 目标文本 - 要查找的文本（部分匹配）
 * @returns {Object|null} 找到的元素或 null
 */
function 查找特定文本元素(xmlContent, 目标文本) {
    console.log("查找包含文本 '" + 目标文本 + "' 的元素...");

    let 所有元素 = 提取文本元素(xmlContent);

    // 查找包含目标文本的元素
    for (let i = 0; i < 所有元素.length; i++) {
        if (所有元素[i].文本.includes(目标文本)) {
            console.log("找到匹配元素: " + JSON.stringify(所有元素[i]));
            return 所有元素[i];
        }
    }

    console.log("未找到包含文本 '" + 目标文本 + "' 的元素");
    return null;
}

/**
 * 查找并点击特定文本的元素
 * @param {string} 目标文本 - 要查找的文本
 * @returns {boolean} 是否成功
 */
function 查找并点击(目标文本) {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return false;

    let 元素 = 查找特定文本元素(xmlContent, 目标文本);
    if (!元素) return false;

    return 点击(元素.坐标.中心X, 元素.坐标.中心Y);
}

/**
 * 查找点赞按钮并点击
 * @returns {boolean} 是否成功
 */
function 查找点赞按钮() {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return false;

    // 先尝试查找包含"点赞"的元素
    let 点赞元素 = 查找特定文本元素(xmlContent, "点赞");

    if (点赞元素) {
        return 点击(点赞元素.坐标.中心X, 点赞元素.坐标.中心Y);
    }

    // 如果没找到，可能需要查找特定的图标或其他标识
    console.log("未找到点赞按钮");
    return false;
}

/**
 * 打印当前界面的所有文本元素
 */
function 打印所有文本元素() {
    let xmlContent = 获取界面XML();
    if (!xmlContent) return;

    let 元素列表 = 提取文本元素(xmlContent);

    console.log("==== 当前界面文本元素 ====");
    for (let i = 0; i < 元素列表.length; i++) {
        let 元素 = 元素列表[i];
        // 跳过空文本内容
        if (!元素.文本 || 元素.文本.trim() === "") continue;

        console.log((i + 1) + ". 文本: [" + 元素.文本 + "], 坐标: (" +
            元素.坐标.中心X + ", " + 元素.坐标.中心Y + ")");
    }
    console.log("==== 共 " + 元素列表.length + " 个有效元素 ====");

    // 标记核心元素
    标记核心元素(元素列表);
}

/**
 * 查找元素
 * @param {string} 选择器 - 元素选择器，如text("文本")或id("id")
 * @returns {UiObject|null} 找到的元素或null
 */
function 查找元素(选择器) {
    if (操作模式 === 1) {
        // 无障碍模式
        try {
            let 元素 = 选择器.findOne(1000);
            return 元素 || null;
        } catch (e) {
            console.error("查找元素出错: " + e.message);
            return null;
        }
    } else {
        // Root Shell模式下无法直接使用无障碍选择器
        console.log("Root Shell模式下不支持直接使用选择器查找元素");
        return null;
    }
}


/**
 * 查找文本元素并点击
 * @param {string} 文本 - 要查找的文本
 * @returns {boolean} 是否成功点击
 */
function 查找文本并点击(文本) {
    console.log("查找并点击文本: " + 文本);

    if (操作模式 === 1) {
        // 无障碍模式
        let 元素 = text(文本).findOne(3000);
        if (元素) {
            元素.click();
            return true;
        }
        return false;
    } else {
        // Root Shell模式
        return 查找并点击(文本);
    }
}


/**
 * 常用操控函数封装 - 使用 shell 命令替代无障碍服务
 * 以下函数都使用 root 权限执行 shell 命令
 */

/**
 * 返回键操作
 * @returns {boolean} 是否执行成功
 */
function 返回() {
    console.log("执行返回操作");

    if (操作模式 === 1) {
        // 无障碍模式
        back();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 4'); // KEYCODE_BACK = 4
        let 成功 = result.code === 0;
        console.log(成功 ? "返回操作成功" : "返回操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 回到主页
 * @returns {boolean} 是否执行成功
 */
function 主页() {
    console.log("执行回到主页操作");

    if (操作模式 === 1) {
        // 无障碍模式
        home();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 3'); // KEYCODE_HOME = 3
        let 成功 = result.code === 0;
        console.log(成功 ? "主页操作成功" : "主页操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 打开最近任务
 * @returns {boolean} 是否执行成功
 */
function 最近任务() {
    console.log("执行打开最近任务操作");

    if (操作模式 === 1) {
        // 无障碍模式
        recents();
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent 187'); // KEYCODE_APP_SWITCH = 187
        let 成功 = result.code === 0;
        console.log(成功 ? "最近任务操作成功" : "最近任务操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 点击屏幕
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @returns {boolean} 是否执行成功
 */
function 点击(x, y) {
    console.log("执行点击操作: (" + x + ", " + y + ")");

    if (操作模式 === 1) {
        // 无障碍模式
        click(x, y);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input tap ' + x + ' ' + y);
        let 成功 = result.code === 0;
        console.log(成功 ? "点击操作成功" : "点击操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 长按屏幕
 * @param {number} x - X 坐标
 * @param {number} y - Y 坐标
 * @param {number} 时长 - 长按时长(毫秒)，默认1000ms
 * @returns {boolean} 是否执行成功
 */
function 长按(x, y, 时长 = 1000) {
    console.log("执行长按操作: (" + x + ", " + y + "), 时长: " + 时长 + "ms");

    if (操作模式 === 1) {
        // 无障碍模式
        press(x, y, 时长);
        return true;
    } else {
        // Root Shell模式 - 使用swipe命令在同一位置停留来模拟长按
        let result = 执行Root命令('input swipe ' + x + ' ' + y + ' ' + x + ' ' + y + ' ' + 时长);
        let 成功 = result.code === 0;
        console.log(成功 ? "长按操作成功" : "长按操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 滑动屏幕
 * @param {number} 起点x - 起点X坐标
 * @param {number} 起点y - 起点Y坐标
 * @param {number} 终点x - 终点X坐标
 * @param {number} 终点y - 终点Y坐标
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 滑动(起点x, 起点y, 终点x, 终点y, 时长 = 500) {
    console.log("执行滑动操作: (" + 起点x + ", " + 起点y + ") -> (" + 终点x + ", " + 终点y + "), 时长: " + 时长 + "ms");

    if (操作模式 === 1) {
        // 无障碍模式
        swipe(起点x, 起点y, 终点x, 终点y, 时长);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input swipe ' + 起点x + ' ' + 起点y + ' ' + 终点x + ' ' + 终点y + ' ' + 时长);
        let 成功 = result.code === 0;
        console.log(成功 ? "滑动操作成功" : "滑动操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 上滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕高度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 上滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 如果未指定距离，默认为屏幕高度的1/3
    距离 = 距离 || Math.floor(屏幕高度 / 3);

    let 起点x = Math.floor(屏幕宽度 / 2);
    let 起点y = Math.floor(屏幕高度 * 0.7);
    let 终点y = 起点y - 距离;

    return 滑动(起点x, 起点y, 起点x, 终点y, 时长);
}

/**
 * 下滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕高度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 下滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 如果未指定距离，默认为屏幕高度的1/3
    距离 = 距离 || Math.floor(屏幕高度 / 3);

    let 起点x = Math.floor(屏幕宽度 / 2);
    let 起点y = Math.floor(屏幕高度 * 0.3);
    let 终点y = 起点y + 距离;

    return 滑动(起点x, 起点y, 起点x, 终点y, 时长);
}

/**
 * 左滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕宽度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 左滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 如果未指定距离，默认为屏幕宽度的1/3
    距离 = 距离 || Math.floor(屏幕宽度 / 3);

    let 起点y = Math.floor(屏幕高度 / 2);
    let 起点x = Math.floor(屏幕宽度 * 0.7);
    let 终点x = 起点x - 距离;

    return 滑动(起点x, 起点y, 终点x, 起点y, 时长);
}

/**
 * 右滑屏幕
 * @param {number} 距离 - 滑动距离，默认屏幕宽度的1/3
 * @param {number} 时长 - 滑动时长(毫秒)，默认500ms
 * @returns {boolean} 是否执行成功
 */
function 右滑(距离 = null, 时长 = 500) {
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 如果未指定距离，默认为屏幕宽度的1/3
    距离 = 距离 || Math.floor(屏幕宽度 / 3);

    let 起点y = Math.floor(屏幕高度 / 2);
    let 起点x = Math.floor(屏幕宽度 * 0.3);
    let 终点x = 起点x + 距离;

    return 滑动(起点x, 起点y, 终点x, 起点y, 时长);
}

/**
 * 输入文本
 * @param {string} 文本 - 要输入的文本
 * @returns {boolean} 是否执行成功
 */
function 输入文本(文本) {
    console.log("执行输入文本操作: " + 文本);

    if (操作模式 === 1) {
        // 无障碍模式
        input(文本);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        // 注意需要转义双引号
        let 安全文本 = 文本.replace(/"/g, '\\"');
        let result = 执行Root命令('input text "' + 安全文本 + '"');
        let 成功 = result.code === 0;
        console.log(成功 ? "输入文本成功" : "输入文本失败: " + result.error);
        return 成功;
    }
}

/**
 * 按下按键
 * @param {number} 按键码 - 按键的keycode
 * @returns {boolean} 是否执行成功
 */
function 按键(按键码) {
    console.log("执行按键操作: " + 按键码);

    if (操作模式 === 1) {
        // 无障碍模式
        keycode(按键码);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('input keyevent ' + 按键码);
        let 成功 = result.code === 0;
        console.log(成功 ? "按键操作成功" : "按键操作失败: " + result.error);
        return 成功;
    }
}

/**
 * 常用按键码
 */
const 按键码 = {
    返回: 4,      // KEYCODE_BACK
    主页: 3,      // KEYCODE_HOME
    菜单: 82,     // KEYCODE_MENU
    搜索: 84,     // KEYCODE_SEARCH
    电源: 26,     // KEYCODE_POWER
    相机: 27,     // KEYCODE_CAMERA
    最近任务: 187, // KEYCODE_APP_SWITCH
    音量加: 24,   // KEYCODE_VOLUME_UP
    音量减: 25,   // KEYCODE_VOLUME_DOWN
    静音: 164,    // KEYCODE_VOLUME_MUTE
    亮度加: 221,  // KEYCODE_BRIGHTNESS_UP
    亮度减: 220,  // KEYCODE_BRIGHTNESS_DOWN
    上: 19,       // KEYCODE_DPAD_UP
    下: 20,       // KEYCODE_DPAD_DOWN
    左: 21,       // KEYCODE_DPAD_LEFT
    右: 22,       // KEYCODE_DPAD_RIGHT
    确定: 23,     // KEYCODE_DPAD_CENTER
    通话: 5,      // KEYCODE_CALL
    挂断: 6,      // KEYCODE_ENDCALL
    锁屏: 223     // KEYCODE_SLEEP
};

/**
 * 启动应用
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否执行成功
 */
function 启动应用(包名) {
    console.log("启动应用: " + 包名);

    if (操作模式 === 1) {
        // 无障碍模式
        app.launch(包名);
        return true;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('am start -n ' + 包名 + '/.MainActivity');

        // 如果上面的命令失败，尝试使用更通用的方式
        if (result.code !== 0) {
            result = 执行Root命令('monkey -p ' + 包名 + ' -c android.intent.category.LAUNCHER 1');
        }

        let 成功 = result.code === 0;
        console.log(成功 ? "启动应用成功" : "启动应用失败: " + result.error);
        return 成功;
    }
}

/**
 * 关闭应用
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否执行成功
 */
function 关闭应用(包名) {
    console.log("关闭应用: " + 包名);

    if (操作模式 === 1) {
        // 无障碍模式
        app.openAppSetting(包名);
        等待(1000);
        let 强行停止 = text("强行停止").findOne(2000);
        if (强行停止) {
            强行停止.click();
            等待(1000);
            let 确认 = text("确定").findOne(2000) ||
                text("确认").findOne() ||
                text("强行停止").findOne();
            if (确认) {
                确认.click();
                等待(1000);
                返回();
                return true;
            }
        }
        返回();
        return false;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('am force-stop ' + 包名);
        let 成功 = result.code === 0;
        console.log(成功 ? "关闭应用成功" : "关闭应用失败: " + result.error);
        return 成功;
    }
}

/**
 * 获取当前应用包名
 * @returns {string} 当前应用包名
 */
function 获取当前应用() {
    console.log("获取当前应用包名");

    if (操作模式 === 1) {
        // 无障碍模式
        let 包名 = currentPackage();
        console.log("当前应用包名: " + 包名);
        return 包名;
    } else {
        // Root Shell模式 - 使用执行Root命令函数
        let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
        if (result.code === 0) {
            let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
            if (match && match[1]) {
                let 当前包名 = match[1];
                console.log("当前应用包名: " + 当前包名);
                return 当前包名;
            }
        }
        console.log("获取当前应用包名失败");
        return null;
    }
}

/**
 * 等待指定时间
 * @param {number} 毫秒 - 等待时间(毫秒)
 */
function 等待(毫秒) {
    console.log("等待 " + 毫秒 + " 毫秒");
    sleep(毫秒);
}

/**
 * 检查应用是否安装
 * @param {string} 包名 - 应用包名
 * @returns {boolean} 是否已安装
 */
function 应用已安装(包名) {
    console.log("检查应用是否安装: " + 包名);

    // 使用执行Root命令函数
    let result = 执行Root命令('pm list packages | grep ' + 包名);
    let 已安装 = result.code === 0 && result.result.includes(包名);
    console.log(已安装 ? "应用已安装" : "应用未安装");
    return 已安装;
}

//=====================
//---------



/**
 * 启动小红书应用
 * 使用root权限
 * @param {number} 等待时间 - 启动后等待时间(毫秒)，默认3000ms
 * @returns {boolean} - 是否成功启动
 */
function 启动小红书(等待时间 = 30) {
    console.log("启动小红书应用");
    try {
        for (let i = 0; i < 3; i++) {
            console.log(`第 ${i + 1} 次尝试启动小红书`);
            console.log("使用monkey命令启动小红书");

            let result = shell("monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1", true);
            console.log(`启动结果: code=${result.code}`);

            // monkey命令的特殊处理：即使返回code=1，也可能启动成功
            // 所以不管返回码如何，都等待并检查是否真的启动了
            console.log("monkey命令执行完成，等待应用启动...");
            等待(3000); // 等待应用启动

            if (检查是否进入小红书()) {
                console.log("✅ 小红书启动成功！");
                return true;
            } else {
                console.log("⚠️ monkey命令执行但未检测到小红书界面，继续重试");
            }

            等待(1000); // 重试间隔
        }
    } catch (e) {
        console.error("启动小红书时出错: " + e.message);
        return false;
    }
    console.log("启动小红书失败了");
    return false;
}



/**
 * 强制退出小红书应用
 * 使用AutoJS官方API关闭小红书应用，无需root权限
 * @returns {boolean} - 是否成功退出
 */
function 强制退出小红书() {
    console.log("强制退出小红书应用");

    try {
        if (操作模式 === 1) {
            // 无障碍模式
            // 方法1: 使用app.killApp方法(AutoJS 6.0.0+)
            if (app.killApp && typeof app.killApp === 'function') {
                let result = app.killApp("com.xingin.xhs");
                console.log("使用app.killApp退出小红书: " + (result ? "成功" : "失败"));
                等待(1000);
                return result;
            }

            // 方法2: 使用app.openAppSetting打开应用设置，然后模拟点击"强行停止"按钮
            console.log("尝试通过应用设置强制停止小红书");
            app.openAppSetting("com.xingin.xhs");
            等待(1000); // 等待设置页面打开

            // 查找"强行停止"按钮并点击
            let 强行停止按钮 = textMatches(/(强.停止|强制.*停止|结束运行|停止运行|force.*stop)/).findOne(2000);
            if (强行停止按钮) {
                强行停止按钮.click();
                等待(1000);

                // 查找确认对话框中的"确定"按钮
                let 确认按钮 = textMatches(/(确定|确认|是|OK|强行停止)/).findOne(2000);
                if (确认按钮) {
                    确认按钮.click();
                    等待(1000);
                    console.log("已成功强制停止小红书");

                    // 返回到之前的界面
                    返回();
                    等待(500);
                    return true;
                }
            } else {
                // 如果没找到强行停止按钮，可能应用已经不在运行
                console.log("未找到强行停止按钮，应用可能已经不在运行");
                返回(); // 返回到之前的界面
                等待(500);
                return true;
            }

            console.log("无法通过应用设置强制停止小红书");
            // 返回到之前的界面
            返回();
            等待(500);
            return false;
        } else {
            // Root模式 - 使用执行Root命令函数
            console.log("使用Root权限强制停止小红书");
            let result = 执行Root命令('am force-stop com.xingin.xhs');
            let 成功 = result.code === 0;
            console.log(成功 ? "Root模式强制停止小红书成功" : "Root模式强制停止小红书失败: " + result.error);
            等待(1000);
            return 成功;
        }
    } catch (e) {
        console.error("强制退出小红书出错: " + e.message);
        // 尝试返回到之前的界面
        try {
            返回();
        } catch (e2) {
            // 忽略返回键错误
        }
        等待(500);
        return false;
    }
}

/**
 * 直接对链接文章进行点赞
 * @param {string} 链接 - 要点赞的文章链接
 * @returns {boolean} - 是否成功点赞
 */
function 直接点赞链接文章(链接) {
    console.log("调用兼容版本的直接点赞链接文章: " + 链接);

    // 调用新版本的直接互动链接文章函数，只执行点赞操作，使用默认配置
    let 操作结果 = 直接互动链接文章(链接, true, false, 3, 0, 0, 0, 0, 10000, 1);

    // 返回点赞是否成功
    return 操作结果.成功 && 操作结果.点赞状态;
}

/**
 * 执行自动点赞主功能 - 兼容旧版本调用
 * 现在调用主函数来执行完整的服务端流程
 */
function 执行自动点赞() {
    console.log("调用兼容版本的执行自动点赞，将启动主函数");
    return 主函数();
}

/**
 * 执行自动互动操作 - 服务端版本
 * 只负责具体的互动操作，不获取链接
 * @param {string} 链接 - 要操作的链接
 * @param {string} 链接ID - 链接ID，用于更新状态
 * @param {boolean} 需要点赞 - 是否需要点赞
 * @param {boolean} 需要收藏 - 是否需要收藏
 * @param {number} 浏览延时 - 浏览延时时间(秒)
 * @param {number} 目标点赞数 - 目标点赞数量
 * @param {number} 目标收藏数 - 目标收藏数量
 * @param {number} 原始点赞数 - 原始点赞数量
 * @param {number} 原始收藏数 - 原始收藏数量
 * @param {number} 阅读延时 - 阅读延时时间(毫秒)
 * @param {number} 阅读开关 - 阅读开关(1开启，0关闭)
 * @returns {Object} 操作结果
 */
function 执行自动互动(链接, 链接ID, 需要点赞 = true, 需要收藏 = false, 浏览延时 = 3, 目标点赞数 = 0, 目标收藏数 = 0, 原始点赞数 = 0, 原始收藏数 = 0, 阅读延时 = 10000, 阅读开关 = 1) {
    console.log(`执行自动互动操作: ${链接}`);
    console.log(`配置: 点赞=${需要点赞}, 收藏=${需要收藏}, 浏览延时=${浏览延时}秒`);
    console.log(`目标数量: 点赞=${目标点赞数}, 收藏=${目标收藏数}`);
    console.log(`原始数量: 点赞=${原始点赞数}, 收藏=${原始收藏数}`);

    try {
        // 使用直接互动链接文章函数处理，传递目标数量参数和阅读配置
        let 结果 = 直接互动链接文章(链接, 需要点赞, 需要收藏, 浏览延时, 目标点赞数, 目标收藏数, 原始点赞数, 原始收藏数, 阅读延时, 阅读开关);

        // 准备更新状态的参数
        let 操作状态 = 结果.成功 ? "success" : "failed";
        let 操作类型 = "";
        if (需要点赞 && 需要收藏) {
            操作类型 = "both";
        } else if (需要点赞) {
            操作类型 = "like";
        } else if (需要收藏) {
            操作类型 = "collect";
        }

        let 错误信息 = 结果.成功 ? null : (结果.原因 || "操作失败");
        let 状态消息 = ""; // 将状态消息变量移到外层作用域

        if (结果.成功) {
            // 检查是否因为达标而跳过操作
            let 点赞已达标 = 结果.点赞已达标 || false;
            let 收藏已达标 = 结果.收藏已达标 || false;

            if (需要点赞 || 点赞已达标) {
                // 判断是新点赞、已经点赞过、还是已达标
                if (结果.点赞成功) {
                    状态消息 += "点赞成功";
                } else if (结果.已完成点赞) {
                    状态消息 += "已点赞，跳过";
                } else if (点赞已达标) {
                    状态消息 += "点赞已达标";
                } else {
                    状态消息 += "点赞失败";
                }
            }

            if (需要收藏 || 收藏已达标) {
                // 判断是新收藏、已经收藏过、还是已达标
                if (结果.收藏成功) {
                    状态消息 += 状态消息 ? ", 收藏成功" : "收藏成功";
                } else if (结果.已完成收藏) {
                    状态消息 += 状态消息 ? ", 已收藏，跳过" : "已收藏，跳过";
                } else if (收藏已达标) {
                    状态消息 += 状态消息 ? ", 收藏已达标" : "收藏已达标";
                } else {
                    状态消息 += 状态消息 ? ", 收藏失败" : "收藏失败";
                }
            }

            console.log(`操作结果: ${状态消息}`);
            toast(状态消息);
        } else {
            状态消息 = 结果.原因 || "操作失败";
            console.error(`处理链接失败: ${状态消息}`);
            toast("处理链接失败: " + 状态消息);
        }

        // 更新服务端状态，传递操作前的数量（与xiaohongshu.js保持一致）
        let 操作前点赞数 = 结果.当前点赞数 || 0;
        let 操作前收藏数 = 结果.当前收藏数 || 0;
        console.log(`提交到服务端的操作前数量: 点赞=${操作前点赞数}, 收藏=${操作前收藏数}`);
        更新操作状态(链接ID, 操作状态, 操作类型, 操作前点赞数, 操作前收藏数, 错误信息);

        // 返回操作结果，包含是否需要操作间隔的信息
        return {
            成功: 结果.成功,
            需要操作间隔: 结果.成功 && (结果.点赞成功 || 结果.收藏成功), // 只有新操作成功才需要间隔
            状态消息: 状态消息,
            详细结果: 结果
        };

    } catch (e) {
        console.error("自动互动操作失败: " + e.message);
    }
}

/**
 * 执行互动操作
 * @param {boolean} 需要点赞 - 是否需要执行点赞操作
 * @param {boolean} 需要收藏 - 是否需要执行收藏操作
 * @returns {Object} 包含操作结果的对象
 */
function 执行互动操作(需要点赞 = true, 需要收藏 = false) {
    console.log(`执行互动操作: 点赞=${需要点赞}, 收藏=${需要收藏}`);

    // 只获取一次XML和状态，避免重复操作
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败，无法执行互动操作");
        return {
            点赞成功: false,
            收藏成功: false,
            已完成点赞: false,
            已完成收藏: false
        };
    }

    let 元素列表 = 提取文本元素(xmlContent);
    let 按钮信息 = 标记核心元素(元素列表);

    if (!按钮信息) {
        console.log("未找到交互按钮位置信息，无法执行互动操作");
        return {
            点赞成功: false,
            收藏成功: false,
            已完成点赞: false,
            已完成收藏: false
        };
    }

    // 检测当前状态
    let 当前状态 = { 已点赞: false, 已收藏: false };

    // 只在需要相应操作时检测状态，节省资源
    if (需要点赞) {
        // 只检测点赞状态
        当前状态.已点赞 = 判断点赞状态(按钮信息);
        console.log(`当前点赞状态: ${当前状态.已点赞 ? "已点赞" : "未点赞"}`);
    }

    if (需要收藏) {
        // 只检测收藏状态
        当前状态.已收藏 = 判断收藏状态(按钮信息);
        console.log(`当前收藏状态: ${当前状态.已收藏 ? "已收藏" : "未收藏"}`);
    }

    let 结果 = {
        点赞成功: false,    // 本次操作是否成功点赞
        收藏成功: false,    // 本次操作是否成功收藏
        已完成点赞: 当前状态.已点赞,  // 当前是否已点赞（包括之前已点赞的情况）
        已完成收藏: 当前状态.已收藏   // 当前是否已收藏（包括之前已收藏的情况）
    };

    // 检查是否是带分享按钮的界面（视频）
    let 带分享按钮 = !!按钮信息.分享按钮;
    let 是视频 = 带分享按钮;
    let 是图文 = !带分享按钮;

    // 执行点赞操作（如果需要点赞且尚未点赞）
    if (需要点赞 && !当前状态.已点赞) {
        console.log("执行点赞操作");

        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;

        // 如果是图文，先检查是否需要下滑回顶部
        if (是图文) {
            // 检查是否已经滑动过，通过查找顶部元素判断
            let 顶部元素 = 元素列表.find(e =>
                e.坐标.中心Y < 屏幕高度 * 0.2 &&
                e.文本.length >= 2 &&
                !["关注", "点赞", "收藏", "评论", "分享"].includes(e.文本)
            );

            if (!顶部元素) {
                console.log("图文可能已滑动，先下滑恢复到顶部");
                下滑(Math.floor(屏幕高度 / 3));
                等待(1000);
            }
        }

        // 随机生成点击次数（3-6次）
        let 点击次数 = 3 + Math.floor(Math.random() * 4);
        console.log(`将进行 ${点击次数} 次双击点赞操作`);

        // 屏幕中心位置
        let 中心X = Math.floor(屏幕宽度 / 2);
        let 中心Y = Math.floor(屏幕高度 / 2);

        for (let i = 0; i < 点击次数; i++) {
            // 随机偏移，范围在200像素内
            let 偏移X = Math.floor(Math.random() * 400) - 200;  // -200 到 200
            let 偏移Y = Math.floor(Math.random() * 400) - 200;  // -200 到 200

            // 确保坐标在屏幕范围内
            let 点击X = Math.max(50, Math.min(屏幕宽度 - 50, 中心X + 偏移X));
            let 点击Y = Math.max(50, Math.min(屏幕高度 - 50, 中心Y + 偏移Y));

            console.log(`第 ${i + 1} 次双击点赞，坐标: (${点击X}, ${点击Y})`);

            // 执行双击操作
            点击(点击X, 点击Y);
            等待(10 + Math.floor(Math.random() * 20));  // 双击间隔50-100ms
            点击(点击X, 点击Y);

            // 点击间隔
            等待(20 + Math.floor(Math.random() * 30));
        }

        等待(1000);

        // 直接假定点赞成功，不再重复检查
        结果.已完成点赞 = true;
        结果.点赞成功 = true;
        console.log("点赞操作完成，假定点赞成功");
    } else if (需要点赞) {
        if (当前状态.已点赞) {
            console.log("已经点过赞，无需重复操作");
            结果.已完成点赞 = true;
            结果.点赞成功 = false;  // 不是本次操作成功的，而是之前已经点过赞
        } else {
            console.log("无法找到点赞数文本，无法执行点赞操作");
        }
    }

    // 执行收藏操作（如果需要收藏且尚未收藏）
    if (需要收藏 && !当前状态.已收藏 && 按钮信息.收藏数文本) {
        console.log("执行收藏操作");

        // 确定点击坐标
        let 收藏X, 收藏Y;
        let 是纯文本按钮 = 按钮信息.收藏数文本.文本 === "0" || 按钮信息.收藏数文本.文本 === "收藏";

        if (是纯文本按钮) {
            // 直接点击文本按钮的中心位置
            收藏X = 按钮信息.收藏数文本.坐标.中心X;
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y;
            console.log(`点击文本收藏按钮中心位置 (${收藏X}, ${收藏Y})`);
        } else if (带分享按钮) {
            // 带分享按钮(视频)时，收藏按钮在收藏数上方
            收藏X = 按钮信息.收藏数文本.坐标.中心X;
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y - 75; // Y向上偏移75像素
            console.log(`视频界面：点击收藏数上方Y偏移位置 (${收藏X}, ${收藏Y})`);
        } else {
            // 无分享按钮(图文)时，直接点击收藏按钮的中心位置
            // 从XML解析中已经获取到了收藏按钮的实际bounds，直接使用中心坐标
            收藏X = 按钮信息.收藏数文本.坐标.中心X;
            收藏Y = 按钮信息.收藏数文本.坐标.中心Y;
            console.log(`图文界面：点击收藏按钮中心位置 (${收藏X}, ${收藏Y})`);
        }

        点击(收藏X, 收藏Y);
        等待(1000);

        // 直接假定收藏成功，不再重复检查
        结果.已完成收藏 = true;
        结果.收藏成功 = true;
        console.log("收藏操作完成，假定收藏成功");
    } else if (需要收藏) {
        if (当前状态.已收藏) {
            console.log("已经收藏过，无需重复操作");
            结果.已完成收藏 = true;
            结果.收藏成功 = false;  // 不是本次操作成功的，而是之前已经收藏过
        } else {
            console.log("无法找到收藏数文本，无法执行收藏操作");
        }
    }

    console.log(`互动操作结果: 点赞=${结果.已完成点赞 ? "已完成" : "未完成"}(${结果.点赞成功 ? "本次成功" : "非本次"}), 收藏=${结果.已完成收藏 ? "已完成" : "未完成"}(${结果.收藏成功 ? "本次成功" : "非本次"})`);
    return 结果;
}


/**
 * 打开链接
 * 
 * @param {string} 链接 - 要打开的链接
 * @returns {boolean} - 是否成功打开
 */
function 浏览器打开链接(链接) {
    // 移除打开链接的详细日志
    // 使用通用方式打开链接
    try {
        app.openUrl(链接);
        toast("已用浏览器打开链接");
    } catch (e) {
        return false;
    }
    const 最大尝试次数 = 20;
    const 每次尝试间隔 = 1000; // 2秒
    const 总超时时间 = 50000; // 50秒
    let 开始时间 = new Date().getTime();

    for (let i = 0; i < 最大尝试次数; i++) {
        if (new Date().getTime() - 开始时间 > 总超时时间) {
            console.warn("OCR识别超时，退出检测");
            break;
        }
        // 首先检查是否已进入小红书
        // if (DeviceOperation.检查是否进入小红书()) {
        //     console.log("已成功跳转到小红书App");
        //     return true;
        // }
        console.log(`第${i + 1}次OCR识别页面...`);
        let 识别内容 = "*App内打开*|*Chrome*|同意|允许|*允许*";
        // OCR识别全屏文字
        let 结果 = null;
        try {
            // 增加错误处理和重试
            let 重试次数 = 0;
            const 最大OCR重试 = 3;

            while (重试次数 < 最大OCR重试) {
                if (检查是否进入小红书()) {
                    console.log("2已成功跳转到小红书App");
                    return true;
                }
                try {
                    结果 = DeviceOperation.获取XML文字信息("", 识别内容, 1, "", true);
                    if (结果 && 结果.length > 0) {
                        DeviceOperation.点击XML关键词(结果, 识别内容);
                        // 点击后等待1秒再检查是否进入小红书
                        sleep(1000);
                        break; // 成功则跳出重试循环
                    }
                } catch (ocrError) {
                    重试次数++;
                    console.error(`OCR识别失败 (${重试次数}/${最大OCR重试}): ${ocrError.message}`);
                    if (重试次数 < 最大OCR重试) {
                        console.log("等待1秒后重试OCR识别...");
                        sleep(1000);
                    } else {
                        console.error("OCR识别重试次数已达上限，跳过本次识别");
                        结果 = null;
                    }
                }
                
            }
        } catch (e) {
            console.error("OCR识别出错: " + e.message);
            // 继续下一次循环，不中断整个流程
        }

        sleep(每次尝试间隔);
    }

    console.warn("未识别到'App内打开'按钮或点击失败");
    return false;
}


/**
 * 打开小红书链接（支持短链接、长链接和笔记ID）
 * 一个方法完成所有操作，自动识别链接类型
 * 
 * @param {string} url - 小红书链接或笔记ID
 * @returns {boolean} - 是否成功打开
 */
function 直接打开小红书链接(url) {
    try {
        // 确保url是字符串类型
        url = String(url);
        console.log("准备打开小红书链接: " + url);
        console.log("URL类型: " + typeof url);
        console.log("URL长度: " + url.length);

        // 如果链接为空
        if (!url || url === "null" || url === "undefined") {
            console.error("无效的链接");
            return false;
        }
        // 情况1: 直接是笔记ID
        if (/^[a-zA-Z0-9_]+$/.test(url) && !url.includes(".")) {
            console.log("检测到笔记ID，直接打开");
            return 直接用ID打开(url);
        }

        // 情况2: 小红书长链接
        if (url.includes("xiaohongshu.com")) {
            console.log("检测到小红书长链接，提取ID");
            console.log("URL类型: " + typeof url);
            console.log("URL内容: " + url);

            // 提取笔记ID
            let noteId = 提取笔记ID(url);
            if (noteId) {
                console.log("从长链接提取到笔记ID: " + noteId);
                return 直接用ID打开(noteId);
            } else {
                return false;
            }
        }
        // 情况3: 小红书短链接，需要先解析
        if (url.includes("xhslink.com")) {
            console.log("检测到小红书短链接，开始解析...");

            // 使用多策略解析短链接
            let resolvedUrl = 解析短链接多策略(url);

            if (resolvedUrl && resolvedUrl !== url) {
                console.log("短链接解析成功: " + resolvedUrl);
                // 递归调用自身处理解析后的URL
                return 直接打开小红书链接(resolvedUrl);
            } else {
                console.log("短链接解析失败，尝试直接打开");
                return false;
            }
        }
        return false;
    } catch (error) {
        console.error("打开小红书链接失败: " + error.message);
        toast("打开小红书链接失败: " + error.message);
        return false;
    }
    // 内部函数：直接用ID打开小红书
    function 直接用ID打开(noteId) {
        try {
            console.log("使用ID打开小红书: " + noteId);

            // 构建深度链接并打开
            app.startActivity({
                action: "android.intent.action.VIEW",
                data: "xhsdiscover://item/" + noteId
            });
            console.log("已发送打开请求");
            return true;
        } catch (e) {
            console.error("打开小红书失败: " + e.message);
            return false;
        }
    }

    // 内部函数：从URL中提取笔记ID
    function 提取笔记ID(url) {
        try {
            // 确保url是字符串类型
            url = String(url);
            console.log("提取笔记ID - 输入URL: " + url);

            // 尝试提取 /explore/[noteId]
            let match = url.match(/\/explore\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                console.log("通过/explore/路径提取到ID: " + match[1]);
                return match[1];
            }
            // 尝试提取 /discovery/item/[noteId]
            match = url.match(/\/discovery\/item\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                // 如果ID包含参数，只取问号前面的部分
                let noteId = match[1].split('?')[0];
                console.log("通过/discovery/item/路径提取到ID: " + noteId);
                return noteId;
            }

            console.log("未能从URL中提取到笔记ID");
            return null;
        } catch (e) {
            console.error("提取笔记ID出错: " + e.message);
            return null;
        }
    }

    // 多策略解析短链接函数
    function 解析短链接多策略(url) {
        console.log("开始多策略解析短链接: " + url);

        // 确保URL格式正确
        url = String(url).trim();
        console.log("原始URL: " + url);
        console.log("URL字符编码检查: " + JSON.stringify(url));

        // 检查URL是否包含非法字符
        if (url.includes('\n') || url.includes('\r') || url.includes('\t')) {
            console.log("URL包含换行符或制表符，进行清理");
            url = url.replace(/[\n\r\t]/g, '');
        }

        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            console.log("URL格式不正确，添加协议前缀");
            url = 'https://' + url;
        }

        console.log("处理后的URL: " + url);

        // 最终验证URL格式
        try {
            let urlObj = new java.net.URL(url);
            console.log("URL验证通过: " + urlObj.toString());
        } catch (e) {
            console.error("URL格式验证失败: " + e.message);
            return null;
        }

        // 策略1: 尝试多种User-Agent
        const userAgents = [
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; U; Android 11; zh-cn; MI 9 Build/RKQ1.200826.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.116 Mobile Safari/537.36"
        ];

        for (let i = 0; i < userAgents.length; i++) {
            try {
                console.log(`尝试User-Agent ${i + 1}/${userAgents.length}`);
                console.log(`请求URL: ${url}`);
                console.log(`URL类型: ${typeof url}`);
                console.log(`URL长度: ${url.length}`);

                // 验证URL格式
                if (!url || typeof url !== 'string') {
                    console.error(`无效的URL: ${url}`);
                    continue;
                }

                // 尝试使用更安全的HTTP请求方法
                let response;
                try {
                    response = http.get(url, {
                        followRedirect: false,
                        timeout: 10000,
                        headers: {
                            "User-Agent": userAgents[i],
                            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
                        }
                    });
                } catch (httpError) {
                    console.error(`HTTP请求异常: ${httpError.message}`);
                    throw httpError;
                }

                console.log(`响应状态码: ${response.statusCode}`);

                // 检查重定向
                if (response.statusCode === 301 || response.statusCode === 302) {
                    let location = response.headers['location'] ||
                        response.headers['Location'] ||
                        response.headers['LOCATION'];

                    if (location) {
                        console.log(`找到重定向URL: ${location}`);
                        return String(location);
                    }
                }

                // 检查响应体中的JavaScript重定向
                if (response.statusCode === 200) {
                    let body = response.body.string();
                    let jsRedirect = body.match(/window\.location\.href\s*=\s*['"]([^'"]+)['"]/);
                    if (jsRedirect) {
                        console.log(`找到JavaScript重定向: ${jsRedirect[1]}`);
                        return String(jsRedirect[1]);
                    }
                }

            } catch (e) {
                console.error(`User-Agent ${i + 1} 请求失败: ${e.message}`);
                console.error(`请求的URL: ${url}`);
                continue;
            }
        }

        // 策略2: 跟随重定向
        try {
            console.log("尝试跟随重定向...");
            console.log(`跟随重定向URL: ${url}`);

            // 再次验证URL
            if (!url || typeof url !== 'string') {
                console.error(`跟随重定向时URL无效: ${url}`);
                return null;
            }

            let response = http.get(url, {
                followRedirect: true,
                timeout: 15000,
                headers: {
                    "User-Agent": userAgents[0]
                }
            });

            if (response.url && response.url !== url) {
                console.log(`跟随重定向后的URL: ${response.url}`);
                return String(response.url);
            }
        } catch (e) {
            console.error("跟随重定向失败: " + e.message);
            console.error(`跟随重定向的URL: ${url}`);
        }

        return null;
    }
}


/**
 * 直接互动链接文章
 * @param {string} 链接 - 文章链接
 * @param {boolean} 需要点赞 - 是否需要点赞
 * @param {boolean} 需要收藏 - 是否需要收藏
 * @param {number} 浏览延时 - 浏览延时时间(秒)
 * @param {number} 目标点赞数 - 目标点赞数量
 * @param {number} 目标收藏数 - 目标收藏数量
 * @param {number} 原始点赞数 - 原始点赞数量
 * @param {number} 原始收藏数 - 原始收藏数量
 * @param {number} 阅读延时 - 阅读延时时间(毫秒)
 * @param {number} 阅读开关 - 阅读开关(1开启，0关闭)
 * @returns {Object} 包含操作结果的对象
 */
function 直接互动链接文章(链接, 需要点赞 = true, 需要收藏 = false, 浏览延时 = 3, 目标点赞数 = 0, 目标收藏数 = 0, 原始点赞数 = 0, 原始收藏数 = 0, 阅读延时 = 10000, 阅读开关 = 1) {
    console.log(`直接互动链接文章: ${链接}`);
    console.log(`操作选项: 点赞=${需要点赞}, 收藏=${需要收藏}, 浏览延时=${浏览延时}秒`);
    console.log(`目标数量: 点赞=${目标点赞数}, 收藏=${目标收藏数}`);
    console.log(`原始数量: 点赞=${原始点赞数}, 收藏=${原始收藏数}`);
    console.log(`阅读配置: 阅读延时=${阅读延时}毫秒, 阅读开关=${阅读开关}`);

    // 强制退出小红书
    //强制退出小红书();
    等待(1000);

    // 打开链接
    //app.openUrl(链接);
    console.log("已打开链接");
    if (!浏览器打开链接(链接)) {
    //if (!直接打开小红书链接(链接)) {
        console.log("无法进入小红书");
        return {
            成功: false,
            原因: "打开链接失败",
            已完成点赞: false,
            已完成收藏: false,
            点赞成功: false,
            收藏成功: false
        };
    }
    等待(2000); // 等待页面加载
    // 处理打开流程
    if (!处理打开流程()) {
        console.log("处理打开流程失败");
        return {
            成功: false,
            原因: "无法进入小红书",
            已完成点赞: false,
            已完成收藏: false,
            点赞成功: false,
            收藏成功: false
        };
    }

    等待(2000); // 等待页面完全加载

    // 获取页面信息，检查点赞状态和内容类型（全程只获取一次XML）
    console.log("获取页面信息，检查点赞状态和内容类型...");
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败");
        return {
            成功: false,
            原因: "无法获取页面信息",
            已完成点赞: false,
            已完成收藏: false,
            点赞成功: false,
            收藏成功: false,
            当前点赞数: 0,
            当前收藏数: 0
        };
    }

    console.log("XML获取成功，开始解析页面元素...");
    let 元素列表 = 提取文本元素(xmlContent);
    let 按钮信息 = 标记核心元素(元素列表);

    console.log("页面元素解析完成，后续操作将复用此数据，不再重复获取XML");

    // 获取当前页面的点赞数和收藏数
    let 当前点赞数 = 0;
    let 当前收藏数 = 0;

    if (按钮信息 && 按钮信息.点赞数文本) {
        当前点赞数 = parseInt(按钮信息.点赞数文本.文本) || 0;
    }
    if (按钮信息 && 按钮信息.收藏数文本) {
        当前收藏数 = parseInt(按钮信息.收藏数文本.文本) || 0;
    }

    console.log(`当前页面数量: 点赞=${当前点赞数}, 收藏=${当前收藏数}`);

    // 检查当前状态
    let 已点赞 = false;
    let 已收藏 = false;

    if (按钮信息 && 按钮信息.点赞数文本) {
        已点赞 = 判断点赞状态(按钮信息);
        console.log(`当前点赞状态: ${已点赞 ? "已点赞" : "未点赞"}`);
    }

    if (按钮信息 && 按钮信息.收藏数文本) {
        已收藏 = 判断收藏状态(按钮信息);
        console.log(`当前收藏状态: ${已收藏 ? "已收藏" : "未收藏"}`);
    }

    // 判断是否需要执行操作（基于目标数量）
    let 点赞已达标 = false;
    let 收藏已达标 = false;

    if (目标点赞数 > 0) {
        // 检查是否有有效的初始值
        // 如果原始点赞数为0或未设置，说明没有初始值，不能通过增长量判断达标
        if (原始点赞数 > 0) {
            // 有初始值时，计算当前相对于原始数量的增长
            let 当前增长 = 当前点赞数 - 原始点赞数;
            点赞已达标 = 当前增长 >= 目标点赞数;
            console.log(`点赞达标检查: 当前增长=${当前增长}, 目标=${目标点赞数}, 已达标=${点赞已达标} (有初始值)`);
        } else {
            // 没有初始值时，不能判断为已达标，需要执行操作
            点赞已达标 = false;
            console.log(`点赞达标检查: 原始点赞数=${原始点赞数}, 目标=${目标点赞数}, 已达标=${点赞已达标} (无初始值，需要操作)`);
        }
    }

    if (目标收藏数 > 0) {
        // 检查是否有有效的初始值
        // 如果原始收藏数为0或未设置，说明没有初始值，不能通过增长量判断达标
        if (原始收藏数 > 0) {
            // 有初始值时，计算当前相对于原始数量的增长
            let 当前增长 = 当前收藏数 - 原始收藏数;
            收藏已达标 = 当前增长 >= 目标收藏数;
            console.log(`收藏达标检查: 当前增长=${当前增长}, 目标=${目标收藏数}, 已达标=${收藏已达标} (有初始值)`);
        } else {
            // 没有初始值时，不能判断为已达标，需要执行操作
            收藏已达标 = false;
            console.log(`收藏达标检查: 原始收藏数=${原始收藏数}, 目标=${目标收藏数}, 已达标=${收藏已达标} (无初始值，需要操作)`);
        }
    }

    // 精准判断操作必要性
    console.log("=== 开始精准判断操作必要性 ===");

    // 1. 检查当前状态与任务要求的匹配度
    let 点赞任务完成 = false;
    let 收藏任务完成 = false;

    if (需要点赞) {
        if (已点赞) {
            console.log("✓ 点赞任务：已完成（当前状态：已点赞）");
            点赞任务完成 = true;
            需要点赞 = false; // 不需要再执行点赞操作
        } else if (点赞已达标) {
            console.log("✓ 点赞任务：已达标（数量已满足目标）");
            点赞任务完成 = true;
            需要点赞 = false; // 不需要再执行点赞操作
        } else {
            console.log("○ 点赞任务：需要执行（当前状态：未点赞，且未达标）");
        }
    } else {
        console.log("- 点赞任务：不需要（任务类型不包含点赞）");
        点赞任务完成 = true; // 不需要点赞就算完成
    }

    if (需要收藏) {
        if (已收藏) {
            console.log("✓ 收藏任务：已完成（当前状态：已收藏）");
            收藏任务完成 = true;
            需要收藏 = false; // 不需要再执行收藏操作
        } else if (收藏已达标) {
            console.log("✓ 收藏任务：已达标（数量已满足目标）");
            收藏任务完成 = true;
            需要收藏 = false; // 不需要再执行收藏操作
        } else {
            console.log("○ 收藏任务：需要执行（当前状态：未收藏，且未达标）");
        }
    } else {
        console.log("- 收藏任务：不需要（任务类型不包含收藏或目标数量为0）");
        收藏任务完成 = true; // 不需要收藏就算完成
    }

    // 2. 判断是否还有执行的必要
    if (点赞任务完成 && 收藏任务完成) {
        console.log("=== 判断结果：所有任务已完成，无需执行任何操作，直接返回成功 ===");
        console.log("跳过浏览延时和互动操作，准备处理下一个链接");
        return {
            成功: true,
            已完成点赞: 已点赞 || 点赞已达标,
            已完成收藏: 已收藏 || 收藏已达标,
            点赞成功: false,  // 不是本次操作成功的
            收藏成功: false,  // 不是本次操作成功的
            点赞已达标: 点赞已达标,
            收藏已达标: 收藏已达标,
            当前点赞数: 当前点赞数,
            当前收藏数: 当前收藏数
        };
    } else {
        console.log(`=== 判断结果：需要执行操作 - 点赞:${需要点赞 ? "是" : "否"}, 收藏:${需要收藏 ? "是" : "否"} ===`);
    }

    // 判断是否为视频（查找分享按钮）
    let 分享按钮 = 元素列表.find(e => e.文本 === "分享");
    let 是否视频 = !!分享按钮;

    console.log(`页面类型: ${是否视频 ? "视频" : "图文"}`);

    // 检查阅读开关，决定是否执行阅读延时
    console.log(`阅读开关: ${阅读开关} (1=开启阅读延时, 其他=跳过阅读延时)`);
    console.log(`阅读延时配置: ${阅读延时}毫秒`);

    if (阅读开关 === 1) {
        // 开始阅读延时
        let 阅读延时秒数 = 阅读延时 / 1000;
        console.log(`阅读开关已开启，开始阅读延时 ${阅读延时秒数} 秒...`);



        // 根据内容类型进行阅读
        if (是否视频) {
            // 视频内容：直接等待阅读延时时间，不滑动
            console.log(`视频内容，直接等待阅读 ${阅读延时秒数} 秒（不滑动）`);
            等待(阅读延时);
        } else {
            // 图文内容：阅读延时期间进行滑动
            console.log(`图文内容，阅读延时 ${阅读延时秒数} 秒期间进行滑动操作`);

            // 先等待一部分时间（阅读延时的40%）
            let 前期等待毫秒 = Math.floor(阅读延时 * 0.4);
            if (前期等待毫秒 < 1000) 前期等待毫秒 = 1000; // 至少等待1秒
            console.log(`前期等待 ${前期等待毫秒 / 1000} 秒`);
            等待(前期等待毫秒);

            // 进行滑动浏览
            let 起点X = 屏幕宽度 * (0.4 + Math.random() * 0.2);
            let 起点Y = 屏幕高度 * (0.7 + Math.random() * 0.2);
            let 终点Y = 屏幕高度 * (0.3 + Math.random() * 0.2);
            let 滑动时间 = 300 + Math.floor(Math.random() * 500);

            console.log("开始滑动浏览图文内容");
            滑动(起点X, 起点Y, 起点X, 终点Y, 滑动时间);

            // 等待剩余的阅读延时时间
            let 剩余等待毫秒 = 阅读延时 - 前期等待毫秒;
            console.log(`滑动完成，继续等待 ${剩余等待毫秒 / 1000} 秒完成阅读延时`);
            等待(剩余等待毫秒);

            // 下滑回顶部准备点赞操作
            console.log("阅读延时完成，下滑回顶部准备点赞操作");
            下滑(Math.floor(屏幕高度 / 2));
            等待(1000);
        }

        console.log("阅读延时完成，开始执行互动操作");
    } else {
        // 阅读开关关闭，跳过阅读延时
        console.log("阅读开关已关闭，跳过阅读延时，直接执行互动操作");
    }

    // 阅读完成，开始执行互动操作
    console.log("阅读完成，开始执行互动操作");

    let 操作结果 = {
        点赞成功: false,
        收藏成功: false,
        已完成点赞: false,
        已完成收藏: false
    };

    // 执行点赞操作（使用已获取的按钮信息，不重复获取XML）
    if (需要点赞) {
        console.log("开始执行点赞操作");

        if (按钮信息 && 按钮信息.点赞数文本) {
            // 使用已获取的按钮信息进行精确点击
            let 点赞X, 点赞Y;
            点赞X = Math.round(屏幕宽度 * 0.5463)
            点赞Y = Math.round(屏幕高度 * 0.9675)
            点击(点赞X, 点赞Y);
            等待(1000);

            操作结果.点赞成功 = true;
            操作结果.已完成点赞 = true;
            console.log("点赞操作完成");
        } else {
            console.log("无法找到点赞数文本，无法执行点赞操作");
        }
    } else {
        console.log("跳过点赞操作（任务已完成或不需要点赞）");
        操作结果.已完成点赞 = 已点赞 || 点赞已达标;
        操作结果.点赞成功 = false; // 不是本次操作成功的
    }

    // 执行收藏操作（使用已获取的按钮信息，不重复获取XML）
    if (需要收藏) {
        console.log("开始执行收藏操作");
        等待(1000); // 等待点赞操作完成

        if (按钮信息 && 按钮信息.收藏数文本) {
            // 使用已获取的按钮信息进行精确点击
            let 收藏X, 收藏Y;
            收藏X = Math.round(屏幕宽度 * 0.7741)
            收藏Y = Math.round(屏幕高度 * 0.9675)
            点击(收藏X, 收藏Y);
            等待(1000);

            操作结果.收藏成功 = true;
            操作结果.已完成收藏 = true;
            console.log("收藏操作完成");
        } else {
            console.log("无法找到收藏数文本，无法执行收藏操作");
        }
    } else {
        console.log("跳过收藏操作（任务已完成或不需要收藏）");
        操作结果.已完成收藏 = 已收藏 || 收藏已达标;
        操作结果.收藏成功 = false; // 不是本次操作成功的
    }

    console.log("所有互动操作完成");

    return {
        成功: true,
        已完成点赞: 操作结果.已完成点赞,
        已完成收藏: 操作结果.已完成收藏,
        点赞成功: 操作结果.点赞成功,
        收藏成功: 操作结果.收藏成功,
        点赞已达标: 点赞已达标,
        收藏已达标: 收藏已达标,
        当前点赞数: 当前点赞数,
        当前收藏数: 当前收藏数
    };

}


// 替换原有的执行点赞函数
/**
 * 执行点赞操作（兼容旧版本调用）
 * @returns {boolean} - 是否成功点赞
 */
function 执行点赞() {
    console.log("执行点赞操作（旧版本兼容）");
    let 操作结果 = 执行互动操作(true, false);
    return 操作结果.已完成点赞;
}

/**
 * 调试交互按钮
 * 检测按钮状态，使用新的X和Y偏移范围
 * @param {boolean} 需要点赞 - 是否需要检测点赞状态
 * @param {boolean} 需要收藏 - 是否需要检测收藏状态
 */
function 调试交互按钮(需要点赞 = true, 需要收藏 = true) {
    console.log("开始检测交互按钮状态");
    console.log(`检测选项: 点赞=${需要点赞}, 收藏=${需要收藏}`);

    // 获取界面元素
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败");
        return;
    }

    let 元素列表 = 提取文本元素(xmlContent);
    let 按钮信息 = 标记核心元素(元素列表);

    if (!按钮信息) {
        console.log("未找到按钮位置");
        return;
    }

    // 输出界面类型信息
    if (按钮信息.分享按钮) {
        console.log("检测到带有分享按钮的视频界面");
    } else {
        console.log("检测到标准界面（无分享按钮）");
    }

    // 根据需要检查点赞状态
    let 点赞状态 = false;
    if (需要点赞) {
        if (!按钮信息.点赞数文本) {
            console.log("未找到点赞数文本，无法检测点赞状态");
        } else {
            点赞状态 = 判断点赞状态(按钮信息);
        }
    } else {
        console.log("跳过点赞状态检测");
    }

    // 根据需要检查收藏状态
    let 收藏状态 = false;
    if (需要收藏) {
        if (!按钮信息.收藏数文本) {
            console.log("未找到收藏数文本，无法检测收藏状态");
        } else {
            收藏状态 = 判断收藏状态(按钮信息);
        }
    } else {
        console.log("跳过收藏状态检测");
    }

    console.log("\n===== 当前按钮状态 =====");
    if (需要点赞) {
        console.log(`点赞状态: ${点赞状态 ? "已点赞" : "未点赞"}`);
    }
    if (需要收藏) {
        console.log(`收藏状态: ${收藏状态 ? "已收藏" : "未收藏"}`);
    }
}

/**
 * 标记核心元素
 * @param {Array} 元素列表 - 元素数组
 * @returns {Object|null} 按钮信息对象或null
 */
function 标记核心元素(元素列表) {
    let 屏幕高度 = device.height;
    let 屏幕宽度 = device.width;

    // 1. 找顶部用户名（图文）
    let 顶部用户名 = null;
    for (let 元素 of 元素列表) {
        if (
            元素.坐标.中心Y < 屏幕高度 * 0.3 &&
            元素.文本.length >= 2 && 元素.文本.length <= 12 &&
            !/^\d+$/.test(元素.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享"].some(k => 元素.文本.includes(k))
        ) {
            顶部用户名 = 元素;
            break;
        }
    }

    // 只在视频时找底部昵称和互动数
    let 底部用户名 = null;
    let 点赞 = null, 收藏 = null, 评论 = null;

    // 先查找分享按钮，判断是否是视频界面
    let 分享按钮 = 元素列表.find(e => e.文本 === "分享");
    let 是视频界面 = !!分享按钮;

    // 查找文本形式的点赞、收藏按钮
    let 点赞按钮 = 元素列表.find(e => e.文本 === "点赞" || e.文本 === "赞");
    let 收藏按钮 = 元素列表.find(e => e.文本 === "收藏");

    // 查找content-desc形式的互动按钮（新增）
    let 点赞描述元素 = 元素列表.find(e => e.类型 === "content-desc" && e.互动类型 === "点赞");
    let 收藏描述元素 = 元素列表.find(e => e.类型 === "content-desc" && e.互动类型 === "收藏");
    let 评论描述元素 = 元素列表.find(e => e.类型 === "content-desc" && e.互动类型 === "评论");

    if (!顶部用户名) {
        // 1. 找"关注"或"已关注"按钮
        let 关注按钮 = 元素列表.find(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            (e.文本 === "关注" || e.文本 === "已关注")
        );

        // 2. 昵称候选
        let 昵称候选 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.5 &&
            e.文本.length >= 2 && e.文本.length <= 12 &&
            !/^[\d.]+万?$/.test(e.文本) &&
            !["关注", "说点什么...", "评论", "发弹幕", "分享", "相关搜索", "课程咨询"].some(k => e.文本.includes(k)) &&
            !e.文本.includes(":")
        );

        if (昵称候选.length > 0) {
            if (关注按钮) {
                // 只选X在关注按钮左侧的，且Y最接近
                let 左侧候选 = 昵称候选
                    .filter(e => e.坐标.中心X < 关注按钮.坐标.中心X)
                    .sort((a, b) => {
                        // Y越接近关注按钮越优先
                        let dyA = Math.abs(a.坐标.中心Y - 关注按钮.坐标.中心Y);
                        let dyB = Math.abs(b.坐标.中心Y - 关注按钮.坐标.中心Y);
                        if (dyA !== dyB) return dyA - dyB;
                        // X越靠近关注按钮越优先
                        return 关注按钮.坐标.中心X - a.坐标.中心X - (关注按钮.坐标.中心X - b.坐标.中心X);
                    });
                if (左侧候选.length > 0) {
                    底部用户名 = 左侧候选[0];
                } else {
                    // 没有左侧的，退回原有Y最大X最小
                    昵称候选.sort((a, b) => {
                        if (b.坐标.中心Y !== a.坐标.中心Y) {
                            return b.坐标.中心Y - a.坐标.中心Y;
                        }
                        return a.坐标.中心X - b.坐标.中心X;
                    });
                    底部用户名 = 昵称候选[0];
                }
            } else {
                // 没有关注按钮，退回原有Y最大X最小
                昵称候选.sort((a, b) => {
                    if (b.坐标.中心Y !== a.坐标.中心Y) {
                        return b.坐标.中心Y - a.坐标.中心Y;
                    }
                    return a.坐标.中心X - b.坐标.中心X;
                });
                底部用户名 = 昵称候选[0];
            }
        }

        // 3. 点赞/收藏/评论数：底部剩下的数字（含"万"）
        let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > 屏幕高度 * 0.7);
        let 互动数字 = 底部元素.filter(e =>
            /^[\d.]+万?$/.test(e.文本)
        );
        // 排除昵称
        if (底部用户名) {
            互动数字 = 互动数字.filter(e => e.文本 !== 底部用户名.文本);
        }
        // 按X坐标排序
        互动数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);

        // 优先使用content-desc形式的元素，因为它们包含准确的数字和状态信息
        点赞 = 点赞描述元素 || 互动数字[0] || 点赞按钮;
        收藏 = 收藏描述元素 || 互动数字[1] || 收藏按钮;
        评论 = 评论描述元素 || 互动数字[2] || null;
    } else {
        // 图文页底部数字（精准：说点什么...右侧依次为点赞/收藏/评论）
        let 说点什么 = 元素列表.find(e =>
            e.文本 === "说点什么..." && e.坐标.中心Y > 屏幕高度 * 0.8
        );
        let 分界X = 说点什么 ? 说点什么.坐标.中心X : 0;
        let 底部数字 = 元素列表.filter(e =>
            e.坐标.中心Y > 屏幕高度 * 0.8 &&
            (/^[\d.]+万$/.test(e.文本) || /^\d+$/.test(e.文本) || ["点赞", "收藏", "评论", "赞"].includes(e.文本)) &&
            e.坐标.中心X > 分界X
        );
        底部数字.sort((a, b) => a.坐标.中心X - b.坐标.中心X);

        // 优先使用content-desc形式的元素，因为它们包含准确的数字和状态信息
        点赞 = 点赞描述元素 || 底部数字[0] || 点赞按钮;
        收藏 = 收藏描述元素 || 底部数字[1] || 收藏按钮;
        评论 = 评论描述元素 || 底部数字[2] || null;
        // 如果为文本，视为0
        if (点赞 && (点赞.文本 === "点赞" || 点赞.文本 === "赞")) 点赞.文本 = "0";
        if (收藏 && 收藏.文本 === "收藏") 收藏.文本 = "0";
        if (评论 && 评论.文本 === "评论") 评论.文本 = "0";
    }

    // 打印标记结果
    if (顶部用户名) {
        打印元素详细信息(顶部用户名, "图文用户名");
    }
    if (底部用户名) {
        打印元素详细信息(底部用户名, "用户昵称");
    }
    if (点赞) {
        打印元素详细信息(点赞, "点赞数");
    }
    if (收藏) {
        打印元素详细信息(收藏, "收藏数");
    }
    if (评论) {
        打印元素详细信息(评论, "评论数");
    }

    // 判断内容类型
    let 是图文 = !!顶部用户名;
    console.log(`内容类型: ${是图文 ? "图文" : "视频"}`);

    // 无论是否有分享按钮，都进行按钮状态检测
    // 只要有点赞数和收藏数就可以进行检测
    if (点赞 && 收藏) {
        if (分享按钮) {
            console.log("检测到分享按钮，使用上方Y偏移检测方式（视频界面）");
        } else {
            console.log("未检测到分享按钮，使用左侧X偏移检测方式（图文界面）");
        }

        let 按钮信息 = {
            点赞数文本: 点赞,
            收藏数文本: 收藏,
            评论数文本: 评论,
            分享按钮: 分享按钮
        };

        return 按钮信息;
    } else {
        console.log("未找到足够的互动元素，无法进行按钮状态检测");
        return null;
    }
}

/**
 * 打印元素详细信息
 * @param {Object} 元素 - 元素对象 
 * @param {string} 标签 - 元素标签
 */
function 打印元素详细信息(元素, 标签) {
    if (!元素) return;
    let 属性文本 = '';
    if (元素.原始属性) {
        for (let k in 元素.原始属性) {
            属性文本 += `${k}: ${元素.原始属性[k]}, `;
        }
        属性文本 = 属性文本.replace(/, $/, '');
    }
    console.log(`${标签}: [${元素.文本}], 坐标: (${元素.坐标.中心X}, ${元素.坐标.中心Y})${属性文本 ? ' | ' + 属性文本 : ''}`);
}

/**
 * 读取链接文件
 * @param {string} 完整路径 - 链接文件的完整路径
 * @returns {Array} 链接列表数组
 */
function 读取链接文件(完整路径) {
    try {
        let 文件 = files.read(完整路径);
        let 链接列表 = 文件.split("\n");

        // 过滤空行并清理链接
        链接列表 = 链接列表.filter(链接 => 链接.trim() !== "").map(链接 => 链接.trim());

        console.log(`成功读取链接文件，共 ${链接列表.length} 条链接`);

        // 调试：打印前几个链接
        for (let i = 0; i < Math.min(3, 链接列表.length); i++) {
            console.log(`链接 ${i + 1}: [${链接列表[i]}] (长度: ${链接列表[i].length})`);
        }

        return 链接列表;
    } catch (e) {
        console.error("读取链接文件失败: " + e);
        return [];
    }
}

/**
 * 处理从浏览器打开到小红书的全流程
 * @returns {boolean} 是否成功进入小红书
 */
function 处理打开流程() {
    // 设置最大尝试次数
    let 最大尝试次数 = 50; // 增加到50次
    let 当前尝试次数 = 0;
    let 连续失败次数 = 0;

    console.log("开始处理打开流程，最大尝试次数: " + 最大尝试次数);

    while (当前尝试次数 < 最大尝试次数) {
        当前尝试次数++;
        console.log("当前尝试次数: " + (当前尝试次数));
        if (检查是否进入小红书()) {
            console.log("已检测到进入小红书应用");
            return true;
        }
        等待(1500);
    }
    return false;

    while (当前尝试次数 < 最大尝试次数) {
        console.log("当前尝试次数: " + (当前尝试次数 + 1));

        // 获取界面XML
        let xmlContent = 获取界面XML();
        if (!xmlContent) {
            连续失败次数++;

            // 如果连续3次获取XML失败，尝试其他方法
            if (连续失败次数 >= 3) {
                console.log("连续多次获取XML失败，尝试其他方法处理");

                // 尝试检查当前应用是否已经是小红书
                if (检查是否进入小红书()) {
                    console.log("已检测到进入小红书应用");
                    return true;
                }

                // 尝试点击屏幕中心位置，可能会触发一些按钮
                let 屏幕中心X = Math.floor(device.width / 2);
                let 屏幕中心Y = Math.floor(device.height / 2);
                console.log(`尝试点击屏幕中心位置: (${屏幕中心X}, ${屏幕中心Y})`);
                点击(屏幕中心X, 屏幕中心Y);
                等待(2000);

                连续失败次数 = 0; // 重置连续失败次数
            }

            当前尝试次数++;
            等待(1500); // 失败后等待更长时间
            continue;
        }

        连续失败次数 = 0; // 成功获取XML，重置连续失败计数

        let 元素列表 = 提取文本元素(xmlContent);

        // 定义需要点击的关键词 - 精准匹配用
        let 关键词列表 = [
            "始终", "Chrome", "确定", "展开", "打开 APP 查看", "App内打开", "打开", "继续",
            "在不登录账号的情况下使用", "同意", "知道了",
            "浏览", "允许", "确认", "继续访问", "我同意",
            // 添加更多可能的精准按钮文本
            "打开方式", "选择浏览器", "使用浏览器打开", "使用Chrome打开",
            "仅本次", "总是", "取消", "是", "否"
        ];

        let 已点击 = false;

        // 检查每个关键词
        for (let i = 0; i < 关键词列表.length; i++) {
            let 关键词 = 关键词列表[i];

            // 寻找精准匹配关键词的文本
            for (let j = 0; j < 元素列表.length; j++) {
                let 项目 = 元素列表[j];

                // 改为精准匹配
                if (项目.文本 === 关键词) {
                    console.log("找到精准匹配关键词: " + 关键词);

                    // 获取元素中心坐标
                    let x = 项目.坐标.中心X;
                    let y = 项目.坐标.中心Y;

                    // 点击该位置
                    点击(x, y);
                    console.log("点击坐标: " + x + ", " + y);

                    已点击 = true;
                    等待(1000); // 等待点击后的反应
                    if (关键词 === "打开 APP 查看" || 关键词 === "App内打开") {
                        等待(5000);
                    }
                    break;
                }
            }

            if (已点击) {
                break;
            }
        }

        // 检查是否已经进入小红书应用
        if (检查是否进入小红书()) {
            console.log("已成功进入小红书");
            return true;
        } else {
            console.log("尚未进入小红书，继续尝试");
        }

        // 如果没有找到任何可点击的元素，增加尝试计数
        if (!已点击) {
            当前尝试次数++;
            console.log("本次尝试未找到可点击元素，尝试次数增加到: " + 当前尝试次数);
            等待(1000);
        }
        等待(1000);
    }

    console.log("达到最大尝试次数，未能成功进入小红书");
    return false;
}

/**
 * 检查是否已进入小红书
 * @returns {boolean} 是否在小红书中
 */
function 检查是否进入小红书() {
    // 使用Root命令获取当前应用包名
    let result = 执行Root命令('dumpsys window | grep mCurrentFocus');
    if (result.code === 0) {
        let match = result.result.match(/mCurrentFocus.+?{.+?(\S+)\/(\S+)}/);
        if (match && match[1]) {
            let 当前包名 = match[1];
            console.log("当前应用包名: " + 当前包名);
            return 当前包名 === "com.xingin.xhs";
        }
    }

    console.log("无法确认是否已进入小红书");
    return false;
}

/**
 * 获取当前页面信息
 * @returns {Object} 包含页面信息的对象
 */
function 获取页面信息() {
    console.log("获取当前页面信息");

    // 获取界面XML
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败");
        return {
            是否视频: false,
            作者: "未知",
            标题: "未知",
            内容: "未知",
            所有文本: []
        };
    }

    let 元素列表 = 提取文本元素(xmlContent);

    // 标记核心元素，这会识别出用户昵称、点赞数等
    标记核心元素(元素列表);

    // 判断是否为视频
    let 是否视频 = false;

    // 查找分享按钮，视频页面通常有分享按钮
    let 分享按钮 = 元素列表.find(e => e.文本 === "分享");
    if (分享按钮) {
        是否视频 = true;
        console.log("检测到分享按钮，判断为视频页面");
    }

    console.log(`页面类型: ${是否视频 ? "视频" : "图文"}`);

    // 获取屏幕尺寸
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 查找底部区域的元素
    let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > 屏幕高度 * 0.7);

    // 查找顶部区域的元素
    let 顶部元素 = 元素列表.filter(e => e.坐标.中心Y < 屏幕高度 * 0.3);

    // 存储所有有效文本，用于后续比较
    let 所有文本 = 元素列表
        .filter(e =>
            e.文本 &&
            e.文本.trim() !== "" &&
            !["说点什么...", "分享", "评论", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"].includes(e.文本)
        )
        .map(e => e.文本);

    console.log("===== 所有有效文本 =====");
    所有文本.forEach((文本, 索引) => {
        console.log(`${索引 + 1}. [${文本}]`);
    });

    // 输出所有文本元素，帮助调试
    console.log("===== 所有文本元素 =====");
    元素列表.forEach((e, index) => {
        console.log(`${index + 1}. 文本: [${e.文本}], 位置: (${e.坐标.中心X}, ${e.坐标.中心Y})`);
    });

    // 尝试找出作者名称 - 根据页面类型使用不同策略
    let 作者 = "未知";
    let 可能的作者元素列表 = [];

    if (是否视频) {
        // 视频页面：作者可能在底部区域
        console.log("视频页面：在底部区域查找作者");
        可能的作者元素列表 = 元素列表.filter(e =>
            e.文本.length >= 2 && e.文本.length <= 20 &&
            !e.文本.match(/^\d+$/) &&
            !["关注", "点赞", "收藏", "评论", "分享", "说点什么...", "相关搜索", "课程咨询"].includes(e.文本) &&
            e.坐标.中心Y > 屏幕高度 * 0.5 && // 在屏幕下半部分
            e.坐标.中心Y < 屏幕高度 * 0.9    // 但不是最底部
        );
    } else {
        // 图文页面：作者可能在顶部区域
        console.log("图文页面：在顶部区域查找作者");
        可能的作者元素列表 = 元素列表.filter(e =>
            e.文本.length >= 2 && e.文本.length <= 20 &&
            !e.文本.match(/^\d+$/) &&
            !["关注", "点赞", "收藏", "评论", "分享", "说点什么...", "相关搜索", "课程咨询"].includes(e.文本) &&
            e.坐标.中心Y < 屏幕高度 * 0.4    // 在屏幕上部
        );
    }

    // 输出所有可能的作者候选
    console.log("===== 可能的作者候选 =====");
    可能的作者元素列表.forEach((e, index) => {
        console.log(`${index + 1}. 文本: [${e.文本}], 位置: (${e.坐标.中心X}, ${e.坐标.中心Y})`);
    });

    // 选择最可能的作者
    if (可能的作者元素列表.length > 0) {
        if (是否视频) {
            // 视频页面：优先选择靠近"关注"按钮的元素
            let 关注按钮 = 元素列表.find(e => e.文本 === "关注" || e.文本 === "已关注");

            if (关注按钮) {
                // 按照与关注按钮的距离排序
                可能的作者元素列表.sort((a, b) => {
                    let 距离A = Math.sqrt(Math.pow(a.坐标.中心X - 关注按钮.坐标.中心X, 2) +
                        Math.pow(a.坐标.中心Y - 关注按钮.坐标.中心Y, 2));
                    let 距离B = Math.sqrt(Math.pow(b.坐标.中心X - 关注按钮.坐标.中心X, 2) +
                        Math.pow(b.坐标.中心Y - 关注按钮.坐标.中心Y, 2));
                    return 距离A - 距离B;
                });
            } else {
                // 没有关注按钮，按Y坐标排序，选择靠下的
                可能的作者元素列表.sort((a, b) => b.坐标.中心Y - a.坐标.中心Y);
            }
        } else {
            // 图文页面：按Y坐标排序，选择靠上的
            可能的作者元素列表.sort((a, b) => a.坐标.中心Y - b.坐标.中心Y);
        }

        作者 = 可能的作者元素列表[0].文本;
        console.log("找到可能的作者: " + 作者);
    }

    // 尝试找出标题 - 增强版
    let 标题 = "未知";
    let 可能的标题元素列表 = [];

    if (是否视频) {
        // 视频页面：标题通常是底部区域中较长的文本，在作者名称下方
        console.log("视频页面：查找底部区域中的标题");

        // 先找到作者元素的位置
        let 作者元素位置 = null;
        if (作者 !== "未知") {
            let 作者元素 = 元素列表.find(e => e.文本 === 作者);
            if (作者元素) {
                作者元素位置 = 作者元素.坐标;
                console.log(`找到作者元素位置: (${作者元素位置.中心X}, ${作者元素位置.中心Y})`);
            }
        }

        // 查找可能的标题元素
        可能的标题元素列表 = 元素列表.filter(e =>
            e.文本.length > 8 &&  // 标题通常较长
            !e.文本.match(/^\d+$/) &&
            !["关注", "点赞", "收藏", "评论", "分享", "说点什么...", "相关搜索", "课程咨询"].includes(e.文本) &&
            e.坐标.中心Y > 屏幕高度 * 0.6  // 在屏幕下半部分
        );

        // 如果找到了作者位置，优先选择作者下方的文本
        if (作者元素位置) {
            // 找出在作者下方的元素
            let 作者下方元素 = 可能的标题元素列表.filter(e =>
                e.坐标.中心Y > 作者元素位置.中心Y &&
                Math.abs(e.坐标.中心X - 作者元素位置.中心X) < 屏幕宽度 * 0.5  // 水平位置接近
            );

            if (作者下方元素.length > 0) {
                console.log("找到作者下方的可能标题元素");
                可能的标题元素列表 = 作者下方元素;
            }
        }
    } else {
        // 图文页面：标题通常在顶部区域，且比作者文本长
        console.log("图文页面：查找顶部区域中的标题");

        // 查找顶部区域中较长的文本
        可能的标题元素列表 = 顶部元素.filter(e =>
            e.文本.length > 8 &&  // 标题通常较长
            !e.文本.match(/^\d+$/) &&
            !["关注", "点赞", "收藏", "评论", "分享", "说点什么...", "相关搜索", "课程咨询"].includes(e.文本)
        );

        // 如果作者已知，排除作者文本
        if (作者 !== "未知") {
            可能的标题元素列表 = 可能的标题元素列表.filter(e => e.文本 !== 作者);
        }

        // 如果顶部区域没找到，尝试在中间区域查找
        if (可能的标题元素列表.length === 0) {
            console.log("顶部区域未找到标题，尝试在中间区域查找");
            可能的标题元素列表 = 元素列表.filter(e =>
                e.文本.length > 8 &&
                !e.文本.match(/^\d+$/) &&
                !["关注", "点赞", "收藏", "评论", "分享", "说点什么...", "相关搜索", "课程咨询"].includes(e.文本) &&
                e.坐标.中心Y > 屏幕高度 * 0.3 &&
                e.坐标.中心Y < 屏幕高度 * 0.7
            );

            // 如果作者已知，排除作者文本
            if (作者 !== "未知") {
                可能的标题元素列表 = 可能的标题元素列表.filter(e => e.文本 !== 作者);
            }
        }
    }

    // 输出所有可能的标题候选
    console.log("===== 可能的标题候选 =====");
    可能的标题元素列表.forEach((e, index) => {
        console.log(`${index + 1}. 文本: [${e.文本}], 位置: (${e.坐标.中心X}, ${e.坐标.中心Y})`);
    });

    // 选择最可能的标题
    if (可能的标题元素列表.length > 0) {
        // 按文本长度排序，选择最长的
        可能的标题元素列表.sort((a, b) => b.文本.length - a.文本.length);

        // 取最长的作为标题
        标题 = 可能的标题元素列表[0].文本;
        console.log("找到可能的标题: " + 标题);

        // 如果标题特别长（超过50字符），可能是正文内容，取前部分
        if (标题.length > 50) {
            let 短标题 = 标题.substring(0, 50).trim();
            // 尝试在标点符号处截断
            let 标点位置 = Math.max(
                短标题.lastIndexOf("。"),
                短标题.lastIndexOf("！"),
                短标题.lastIndexOf("？"),
                短标题.lastIndexOf("，"),
                短标题.lastIndexOf("；"),
                短标题.lastIndexOf("、"),
                短标题.lastIndexOf("：")
            );
            if (标点位置 > 10) {  // 确保标题不会太短
                短标题 = 短标题.substring(0, 标点位置 + 1);
            }
            console.log("标题过长，截取为: " + 短标题);
            标题 = 短标题;
        }
    }

    // 尝试找出内容 - 增强版
    let 内容 = "未知";
    let 可能的内容元素列表 = 元素列表.filter(e =>
        e.文本.length > 10 &&
        !["关注", "点赞", "收藏", "评论", "分享", "说点什么...", "相关搜索", "课程咨询"].includes(e.文本) &&
        e.坐标.中心Y > 屏幕高度 * 0.3 &&
        e.坐标.中心Y < 屏幕高度 * 0.9
    );

    // 输出所有可能的内容候选
    console.log("===== 可能的内容候选 =====");
    可能的内容元素列表.forEach((e, index) => {
        console.log(`${index + 1}. 文本: [${e.文本}], 位置: (${e.坐标.中心X}, ${e.坐标.中心Y})`);
    });

    // 选择最可能的内容
    if (可能的内容元素列表.length > 0) {
        // 按文本长度排序，选择最长的
        可能的内容元素列表.sort((a, b) => b.文本.length - a.文本.length);
        内容 = 可能的内容元素列表[0].文本;
        console.log("找到可能的内容: " + 内容);
    }

    // 输出最终结果
    console.log("===== 页面信息提取结果 =====");
    console.log(`是否视频: ${是否视频}`);
    console.log(`作者: ${作者}`);
    console.log(`标题: ${标题}`);
    console.log(`内容: ${内容.substring(0, 50)}${内容.length > 50 ? "..." : ""}`);
    console.log(`所有文本数量: ${所有文本.length}`);

    return {
        是否视频: 是否视频,
        作者: 作者,
        标题: 标题,
        内容: 内容,
        所有文本: 所有文本
    };
}

/**
 * 返回小红书主界面
 * @returns {boolean} 是否成功返回主界面
 */
function 返回主界面() {
    console.log("尝试返回小红书主界面");

    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播", "视频", "购物", "消息", "短剧", "附近", "热门", "我", "推荐", "穿搭", "情感"];

    // 最多尝试5次返回操作
    for (let i = 0; i < 5; i++) {
        console.log("执行第 " + (i + 1) + " 次返回操作");
        返回();
        等待(1500);

        // 检查是否已经回到主界面
        let xmlContent = 获取界面XML();
        if (!xmlContent) continue;

        let 元素列表 = 提取文本元素(xmlContent);

        // 计算找到的首页关键词数量
        let 找到的关键词 = 0;
        for (let j = 0; j < 元素列表.length; j++) {
            let 文本内容 = 元素列表[j].文本;
            if (!文本内容) continue;

            // 检查是否包含任何关键词
            for (let k = 0; k < 首页关键词.length; k++) {
                if (文本内容.includes(首页关键词[k])) {
                    找到的关键词++;
                    break; // 一个元素只计算一次
                }
            }
        }

        console.log(`找到 ${找到的关键词} 个首页关键词`);

        // 如果找到5个或以上关键词，认为已在首页
        if (找到的关键词 >= 5) {
            console.log("已返回主界面");
            return true;
        }
    }

    console.log("无法返回主界面，尝试强制退出小红书");
    强制退出小红书();
    return false;
}

/**
 * 执行需要Root权限的shell命令
 * @param {string} 命令 - 要执行的shell命令
 * @returns {Object} 包含执行结果的对象
 */
function 执行Root命令(命令) {
    console.log("执行Root命令: " + 命令);

    // 检查是否已经获取root权限
    if (!root会话) {
        if (!初始化Root权限()) {
            console.log("未获取到root权限，无法执行命令");
            return {
                code: -1,
                result: "",
                error: "未获取root权限"
            };
        }
    }

    // 使用su -c执行命令
    try {
        let result = shell("su -c '" + 命令 + "'", true);

        if (result.code === 0) {
            console.log("命令执行成功");
            return {
                code: 0,
                result: result.result,
                error: ""
            };
        } else {
            console.log("命令执行失败: " + result.error);
            return {
                code: result.code,
                result: result.result,
                error: result.error
            };
        }
    } catch (e) {
        console.error("执行命令出错: " + e);
        return {
            code: -1,
            result: "",
            error: e.toString()
        };
    }
}

/**
 * 获取交互按钮位置（基于文本偏移）
 * @param {Array} 文本元素列表 - 文本元素数组
 * @returns {Object} 包含点赞、收藏和评论按钮位置的对象
 */
function 获取交互按钮位置(文本元素列表) {
    // 此函数已被标记核心元素函数替代，保留函数名以兼容旧代码
    console.log("警告：获取交互按钮位置函数已被标记核心元素函数替代");
    return 标记核心元素(文本元素列表);
}

/**
 * 判断按钮状态
 * @param {Object} 按钮信息 - 包含点赞数文本、收藏数文本等信息的对象
 * @returns {Object} 包含已点赞和已收藏状态的对象
 */
function 判断按钮状态(按钮信息) {
    console.log("开始判断按钮状态");

    if (!按钮信息 || !按钮信息.点赞数文本) {
        console.log("按钮信息不完整，无法判断状态");
        return {
            已点赞: false,
            已收藏: false
        };
    }

    // 检查是否是带分享按钮的界面
    let 带分享按钮 = !!按钮信息.分享按钮;
    console.log(`界面类型: ${带分享按钮 ? "带分享按钮(视频)" : "无分享按钮(图文)"}`);

    // 分别判断点赞和收藏状态
    let 已点赞 = 判断点赞状态(按钮信息);
    let 已收藏 = 按钮信息.收藏数文本 ? 判断收藏状态(按钮信息) : false;

    console.log(`按钮状态: 已点赞=${已点赞}, 已收藏=${已收藏}`);
    return {
        已点赞: 已点赞,
        已收藏: 已收藏
    };
}

/**
 * 判断点赞状态
 * @param {Object} 按钮信息 - 包含点赞数文本等信息的对象
 * @returns {boolean} 是否已点赞
 */
function 判断点赞状态(按钮信息) {
    console.log("开始判断点赞状态");

    if (!按钮信息 || !按钮信息.点赞数文本) {
        console.log("按钮信息不完整，无法判断点赞状态");
        return false;
    }

    // 优先检查content-desc形式的状态（新增）
    if (按钮信息.点赞数文本.类型 === "content-desc") {
        let 已点赞 = 按钮信息.点赞数文本.已操作;
        console.log(`通过content-desc判断点赞状态: ${已点赞 ? "已点赞" : "未点赞"}`);
        return 已点赞;
    }

    // 如果没有content-desc信息，默认返回未点赞状态
    console.log("无法获取content-desc信息，默认返回未点赞状态");
    return false;
}

/**
 * 判断收藏状态
 * @param {Object} 按钮信息 - 包含收藏数文本等信息的对象
 * @returns {boolean} 是否已收藏
 */
function 判断收藏状态(按钮信息) {
    console.log("开始判断收藏状态");

    if (!按钮信息 || !按钮信息.收藏数文本) {
        console.log("按钮信息不完整，无法判断收藏状态");
        return false;
    }

    // 优先检查content-desc形式的状态（新增）
    if (按钮信息.收藏数文本.类型 === "content-desc") {
        let 已收藏 = 按钮信息.收藏数文本.已操作;
        console.log(`通过content-desc判断收藏状态: ${已收藏 ? "已收藏" : "未收藏"}`);
        return 已收藏;
    }
    // 如果没有content-desc信息，默认返回未收藏状态
    console.log("无法获取content-desc信息，默认返回未收藏状态");
    return false;
}

/**
 * 点击首页第一篇文章
 * @returns {boolean} 是否成功点击
 */
function 点击首篇文章() {
    console.log("尝试点击首页第一篇文章");

    // 获取界面XML
    let xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败，无法点击首篇文章");
        return false;
    }

    // 分析界面，确定是否在首页
    let 元素列表 = 提取文本元素(xmlContent);

    // 检查是否有首页、购物等底部标签
    let 首页标签 = 元素列表.find(e => e.文本 === "首页");
    let 购物标签 = 元素列表.find(e => e.文本 === "购物");

    if (!首页标签 && !购物标签) {
        console.log("未检测到首页标签，可能不在主界面");
        return false;
    }

    // 随机化点击坐标（参考xhs.js中的实现）
    let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
    let y = 400 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20

    console.log(`点击首页文章位置: (${x}, ${y})`);
    点击(x, y);

    // 等待页面加载
    等待(3000);

    // 检查是否成功进入文章页面
    xmlContent = 获取界面XML();
    if (!xmlContent) {
        console.log("获取界面XML失败，无法确认是否进入文章页面");
        return false;
    }

    元素列表 = 提取文本元素(xmlContent);

    // 计算屏幕尺寸
    let 屏幕宽度 = device.width;
    let 屏幕高度 = device.height;

    // 文章页面特征：底部有点赞、收藏、评论等互动元素
    let 底部元素 = 元素列表.filter(e => e.坐标.中心Y > 屏幕高度 * 0.8);
    let 互动数字 = 底部元素.filter(e => /^[\d.]+万?$/.test(e.文本) || /^\d+$/.test(e.文本));

    // 检查底部标签是否消失（首页、购物等不应该在文章页面出现）
    let 底部标签消失 = !元素列表.find(e => e.文本 === "首页" || e.文本 === "购物" || e.文本 === "消息");

    if (互动数字.length >= 2 || 底部标签消失) {
        console.log("检测到互动元素或底部标签消失，成功进入文章页面");
        return true;
    }

    // 额外检查：尝试查找页面信息
    let 页面信息 = 获取页面信息();
    if (页面信息 && (页面信息.作者 !== "未知" || 页面信息.是否视频 === true)) {
        console.log("通过页面信息确认进入文章页面");
        return true;
    }

    console.log("未检测到文章页面特征，可能点击失败");
    return false;
}

/**
 * 比较两个页面信息是否为同一篇文章
 * @param {Object} 信息1 - 第一个页面信息
 * @param {Object} 信息2 - 第二个页面信息
 * @returns {boolean} 是否为同一篇文章
 */
function 比较页面信息(信息1, 信息2) {
    console.log("\n===========================================================");
    console.log("=============== 【页面信息详细对比结果】 ===============");
    console.log("===========================================================\n");

    // 基本信息对比
    console.log("【基本信息对比】");
    console.log("┌──────────┬─────────────────────────────────┬─────────────────────────────────┐");
    console.log("│  对比项  │          原始页面信息          │          新打开页面信息        │");
    console.log("├──────────┼─────────────────────────────────┼─────────────────────────────────┤");
    console.log(`│   作者   │ ${信息1.作者.padEnd(30)} │ ${信息2.作者.padEnd(30)} │`);
    console.log(`│   类型   │ ${(信息1.是否视频 ? "视频" : "图文").padEnd(30)} │ ${(信息2.是否视频 ? "视频" : "图文").padEnd(30)} │`);
    console.log(`│   标题   │ ${(信息1.标题.length > 28 ? 信息1.标题.substring(0, 25) + "..." : 信息1.标题).padEnd(30)} │ ${(信息2.标题.length > 28 ? 信息2.标题.substring(0, 25) + "..." : 信息2.标题).padEnd(30)} │`);
    console.log(`│ 内容长度 │ ${(信息1.内容 !== "未知" ? 信息1.内容.length.toString() : "未知").padEnd(30)} │ ${(信息2.内容 !== "未知" ? 信息2.内容.length.toString() : "未知").padEnd(30)} │`);
    console.log(`│ 文本数量 │ ${信息1.所有文本.length.toString().padEnd(30)} │ ${信息2.所有文本.length.toString().padEnd(30)} │`);
    console.log("└──────────┴─────────────────────────────────┴─────────────────────────────────┘\n");

    // 检查作者是否有效
    if (信息1.作者 === "未知" || 信息2.作者 === "未知") {
        console.log("❌ 无法比较: 作者信息缺失");
        return false;
    }

    // 条件1: 作者必须匹配
    let 作者匹配 = 信息1.作者 === 信息2.作者;
    if (作者匹配) {
        console.log("✅ 条件1满足: 作者完全匹配 [" + 信息1.作者 + "]");
    } else {
        console.log("❌ 条件1不满足: 作者不匹配 [" + 信息1.作者 + "] ≠ [" + 信息2.作者 + "]");
        return false; // 作者不匹配直接返回false
    }

    // 条件2: 页面类型一致（都是视频或都是图文）
    let 类型匹配 = 信息1.是否视频 === 信息2.是否视频;
    if (类型匹配) {
        console.log(`✅ 条件2满足: 页面类型一致 [${信息1.是否视频 ? "视频" : "图文"}]`);
    } else {
        console.log(`❌ 条件2不满足: 页面类型不一致 [${信息1.是否视频 ? "视频" : "图文"}] ≠ [${信息2.是否视频 ? "视频" : "图文"}]`);
    }

    // 条件3: 文本数量一致
    let 文本数量匹配 = 信息1.所有文本.length === 信息2.所有文本.length;
    if (文本数量匹配) {
        console.log(`✅ 条件3满足: 文本数量一致 [${信息1.所有文本.length}]`);
    } else {
        console.log(`❌ 条件3不满足: 文本数量不一致 [${信息1.所有文本.length}] ≠ [${信息2.所有文本.length}]`);
    }

    // 标题对比（仅作参考，不影响最终判断）
    console.log("\n【标题对比】(仅供参考)");
    if (信息1.标题 !== "未知" && 信息2.标题 !== "未知") {
        console.log("原始页面标题: [" + 信息1.标题 + "]");
        console.log("新打开页面标题: [" + 信息2.标题 + "]");

        if (信息1.标题 === 信息2.标题) {
            console.log("✅ 标题完全匹配");
        } else if (信息1.标题.includes(信息2.标题) || 信息2.标题.includes(信息1.标题)) {
            console.log("✅ 标题部分匹配（一个标题包含另一个）");
        } else {
            console.log("❌ 标题不匹配");
        }
    } else {
        console.log("❓ 标题信息不完整，无法比较");
    }

    // 文本匹配度分析（仅作参考，不影响最终判断）
    console.log("\n【文本匹配度分析】(仅供参考)");

    let 匹配文本数量 = 0;
    let 匹配文本列表 = [];

    // 找出匹配的文本
    信息1.所有文本.forEach(文本1 => {
        // 检查文本1是否在信息2的所有文本中
        let 匹配 = 信息2.所有文本.find(文本2 => 文本2 === 文本1);

        if (匹配) {
            匹配文本数量++;
            匹配文本列表.push(文本1);
        }
    });

    // 计算匹配率
    let 总比较文本数 = Math.max(信息1.所有文本.length, 信息2.所有文本.length);
    let 匹配率 = 总比较文本数 > 0 ? (匹配文本数量 / 总比较文本数) : 0;

    console.log(`文本匹配总结: ${匹配文本数量}/${总比较文本数} 匹配 (${(匹配率 * 100).toFixed(2)}%)`);

    // 显示部分匹配的文本（最多显示5个）
    if (匹配文本列表.length > 0) {
        console.log("\n✅ 部分匹配文本示例:");
        匹配文本列表.slice(0, 5).forEach((文本, 索引) => {
            let 显示文本 = 文本.length > 40 ? 文本.substring(0, 37) + "..." : 文本;
            console.log(`${索引 + 1}. ${显示文本}`);
        });
        if (匹配文本列表.length > 5) {
            console.log(`... 还有 ${匹配文本列表.length - 5} 条匹配文本未显示`);
        }
    }

    // 判断最终结果 - 三个条件都满足
    let 是同一篇文章 = 作者匹配 && 类型匹配 && 文本数量匹配;

    console.log("\n【最终判定】");
    if (是同一篇文章) {
        console.log("✅✅✅ 判定结果: 是同一篇文章");
        console.log("满足全部判定条件:");
        console.log(" - 作者一致");
        console.log(` - 类型一致 (${信息1.是否视频 ? "视频" : "图文"})`);
        console.log(` - 文本数量一致 (${信息1.所有文本.length}个)`);
    } else {
        console.log("❌❌❌ 判定结果: 不是同一篇文章");
        console.log("未满足全部判定条件:");
        if (!作者匹配) console.log(" - 作者不一致");
        if (!类型匹配) console.log(` - 类型不一致 (${信息1.是否视频 ? "视频" : "图文"} vs ${信息2.是否视频 ? "视频" : "图文"})`);
        if (!文本数量匹配) console.log(` - 文本数量不一致 (${信息1.所有文本.length} vs ${信息2.所有文本.length})`);
    }
    console.log("\n===========================================================\n");

    return 是同一篇文章;
}

/**
 * 主入口函数 - 服务端版本
 * 可以通过API调用或直接执行
 */
function 小红书点赞操作入口() {
    console.log("=== 小红书自动互动工具 - 服务端版本 ===");
    console.log("正在初始化...");

    // 检查操作模式
    if (操作模式 === 2) {
        console.log("当前使用Root Shell模式");
        if (!初始化Root权限()) {
            console.error("Root权限初始化失败，无法继续执行");
            return false;
        }
    } else {
        console.log("当前使用无障碍模式");
    }

    // 设备注册
    console.log("正在进行设备注册...");
    let 注册结果 = 设备注册();
    if (!注册结果.success) {
        console.error("设备注册失败:", 注册结果.message);
        return false;
    }

    console.log("设备注册成功，开始获取操作信息...");

    // 直接从注册结果中获取操作信息，包含最大操作次数
    let 操作信息 = {
        操作次数: 注册结果.data.operation_count || 0,
        最大操作次数: 注册结果.data.max_operations || 0,
        剩余操作次数: 注册结果.data.remaining_operations || 0
    };
    console.log("当前操作信息: " + JSON.stringify(操作信息));

    let 已完成操作次数 = 0;
    let 最大循环次数 = 操作信息.最大操作次数 || 0; // 从服务端获取最大操作次数

    console.log(`开始循环操作，从服务端获取最大操作次数: ${最大循环次数} 次`);
    toast(`开始循环操作，将执行 ${最大循环次数} 次操作`);

    // 启动小红书应用
    console.log("启动小红书应用...");
    if (!启动小红书(50)) {
        console.error("启动小红书失败，无法继续执行");
        return false;
    }

    // 主循环 - 按固定次数循环
    while (已完成操作次数 < 最大循环次数) {
        try {
            // 获取链接
            console.log(`[${已完成操作次数 + 1}/${最大循环次数}] 获取链接中...`);
            let 获取链接结果 = 获取下一个链接();

            // 检查获取链接的结果
            if (!获取链接结果.success) {
                console.log("获取链接请求失败，等待30秒后重试");
                toast("获取链接请求失败，等待30秒后重试");
                // 等待30秒
                for (let i = 30; i > 0; i--) {
                    if (i % 5 === 0) { // 每5秒输出一次日志和提示
                        console.log(`请求失败，等待中...(${i}秒)`);
                        toast(`请求失败，等待中...(${i}秒)`);
                    }
                    sleep(1000);
                }
                continue; // 继续下一次循环，不增加计数
            }

            // 检查具体的状态
            if (获取链接结果.status === "limit_reached") {
                console.log("已达到每日操作上限，结束循环");
                toast("今日操作次数已达上限，操作结束");
                break; // 退出循环
            }

            if (获取链接结果.status === "no_links") {
                console.log("暂无可操作链接，等待30秒后重试");
                toast("暂无链接，等待30秒后重试");
                // 等待30秒
                for (let i = 30; i > 0; i--) {
                    if (i % 5 === 0) { // 每5秒输出一次日志和提示
                        console.log(`暂无链接，等待中...(${i}秒)`);
                        toast(`暂无链接，等待中...(${i}秒)`);
                    }
                    sleep(1000);
                }
                continue; // 继续下一次循环，不增加计数
            }

            if (获取链接结果.status !== "success") {
                console.log(`未知状态: ${获取链接结果.status}，等待30秒后重试`);
                toast(`未知状态，等待30秒后重试`);
                // 等待30秒
                for (let i = 30; i > 0; i--) {
                    if (i % 5 === 0) { // 每5秒输出一次日志和提示
                        console.log(`未知状态，等待中...(${i}秒)`);
                        toast(`未知状态，等待中...(${i}秒)`);
                    }
                    sleep(1000);
                }
                continue; // 继续下一次循环，不增加计数
            }

            // 获取链接信息
            let 链接 = 获取链接结果.data.url;
            let 链接ID = 获取链接结果.data.link_id;

            // 获取目标数量信息（新增）
            let 目标点赞数 = 获取链接结果.data.target_likes || 0;
            let 目标收藏数 = 获取链接结果.data.target_collects || 0;
            let 原始点赞数 = 获取链接结果.data.original_likes || 0;
            let 原始收藏数 = 获取链接结果.data.original_collects || 0;

            console.log(`获取到链接: ${链接}`);
            console.log(`链接ID: ${链接ID}`);
            console.log(`目标数量 - 点赞: ${目标点赞数}, 收藏: ${目标收藏数}`);
            console.log(`原始数量 - 点赞: ${原始点赞数}, 收藏: ${原始收藏数}`);

            // 保存链接ID
            storages.create("小红书操作").put("当前链接ID", 链接ID);

            // 根据目标数量判断任务类型（更准确的判断方式）
            let 任务类型 = 获取链接结果.data.operation_type || "both";
            let 需要点赞 = (任务类型 === "like" || 任务类型 === "both") && 目标点赞数 > 0;
            let 需要收藏 = (任务类型 === "collect" || 任务类型 === "both") && 目标收藏数 > 0;

            console.log(`任务类型: ${任务类型}, 目标点赞数: ${目标点赞数}, 目标收藏数: ${目标收藏数}`);
            console.log(`实际需要: 点赞=${需要点赞}, 收藏=${需要收藏}`);

            // 获取配置信息 - 使用全局变量或默认值
            let 浏览延时 = 3; // 浏览延时目前没有实际使用，保持默认值
            let 操作间隔 = 5; // 操作间隔使用默认值
            let 阅读延时 = global.阅读延时 || 10000; // 优先使用全局变量，默认10秒
            let 阅读开关 = global.阅读开关 !== undefined ? global.阅读开关 : 0; // 优先使用全局变量，默认关闭

            console.log(`配置信息 - 浏览延时: ${浏览延时}秒, 操作间隔: ${操作间隔}秒, 阅读延时: ${阅读延时}毫秒, 阅读开关: ${阅读开关}`);

            // 使用执行自动互动函数处理链接
            console.log("使用执行自动互动函数处理链接...");
            let 结果 = 执行自动互动(链接, 链接ID, 需要点赞, 需要收藏, 浏览延时, 目标点赞数, 目标收藏数, 原始点赞数, 原始收藏数, 阅读延时, 阅读开关);

            if (结果.成功) {
                console.log(`操作成功: ${结果.状态消息}`);
                toast(`[${已完成操作次数 + 1}/${最大循环次数}] ${结果.状态消息}`);

                // 根据是否需要操作间隔来决定等待时间
                if (结果.需要操作间隔) {
                    console.log(`执行了新操作，需要操作间隔 ${操作间隔} 秒`);
                    for (let j = 0; j < 操作间隔; j++) {
                        console.log(`等待 ${操作间隔 - j} 秒后处理下一条链接`);
                        toast(`等待 ${操作间隔 - j} 秒后处理下一条链接`);
                        sleep(1000);
                    }
                } else {
                    console.log("已点赞/已收藏，跳过操作间隔");
                    toast("跳过操作间隔，直接处理下一条链接");
                    sleep(500); // 短暂等待，避免过快
                }
            } else {
                console.error(`操作失败: ${结果.状态消息}`);
                toast(`[${已完成操作次数 + 1}/${最大循环次数}] 操作失败: ${结果.状态消息}`);

                // 失败时也需要操作间隔
                for (let j = 0; j < 操作间隔; j++) {
                    console.log(`处理失败，等待 ${操作间隔 - j} 秒后处理下一条链接`);
                    toast(`处理失败，等待 ${操作间隔 - j} 秒后处理下一条链接`);
                    sleep(1000);
                }
            }

            // 增加已完成操作次数
            已完成操作次数++;

            // 检查是否达到每日操作上限（在操作完成后检查）
            if (获取链接结果.status === "limit_reached") {
                console.log("当前操作已完成，检测到已达到每日操作上限，正常结束循环");
                toast("当前操作已完成，今日操作次数已达上限，脚本正常结束");
                sleep(3000);
                break; // 在操作完成后正常结束循环
            }

            // 返回到主界面，准备处理下一个链接
            返回();

        } catch (e) {
            console.error("主循环执行出错:", e.message);
            toast("主循环执行出错: " + e.message);
            等待(10000); // 出错后等待10秒再继续
        }
    }

    console.log("程序执行完成");
    return true;
}

// 导出主要函数供外部调用（仅在Node.js环境中）
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
    module.exports = {
        主函数: 小红书点赞操作入口,  // 为了兼容性，将主函数指向小红书点赞操作入口
        服务端执行主功能,
        设备注册,
        获取下一个链接,
        更新操作状态,
        直接互动链接文章,
        执行自动互动,
        小红书点赞操作入口
    };
} else {
    // AutoJS环境中，将函数挂载到全局对象
    global.小红书API = {
        主函数: 小红书点赞操作入口,  // 为了兼容性，将主函数指向小红书点赞操作入口
        服务端执行主功能,
        设备注册,
        获取下一个链接,
        更新操作状态,
        直接互动链接文章,
        执行自动互动,
        小红书点赞操作入口
    };
}

// 在AutoJS环境中直接启动主函数
// if (typeof auto !== 'undefined') {
//     console.log("AutoJS环境检测到，启动主函数...");
//     小红书点赞操作入口();
//     do {
//         等待(3000);
//         toast("操作结束了");
//     } while (1)

// }

